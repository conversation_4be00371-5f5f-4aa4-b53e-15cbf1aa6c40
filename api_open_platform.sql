/*
 Navicat Premium Dump SQL

 Source Server         : ok_local
 Source Server Type    : MySQL
 Source Server Version : 80042 (8.0.42)
 Source Host           : localhost:3306
 Source Schema         : api_open_platform

 Target Server Type    : MySQL
 Target Server Version : 80042 (8.0.42)
 File Encoding         : 65001

 Date: 02/08/2025 18:46:52
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for alert_notification_configs
-- ----------------------------
DROP TABLE IF EXISTS `alert_notification_configs`;
CREATE TABLE `alert_notification_configs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `notification_type` varchar(20) NOT NULL COMMENT '通知类型:email,sms',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用该通知方式',
  `high_level_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否接收高级告警',
  `medium_level_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否接收中级告警',
  `low_level_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否接收低级告警',
  `critical_level_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否接收紧急告警',
  `notification_target` varchar(255) NOT NULL COMMENT '通知目标:邮箱地址/手机号',
  `retry_times` tinyint DEFAULT '3' COMMENT '重试次数',
  `retry_interval` int DEFAULT '300' COMMENT '重试间隔(秒)',
  `quiet_hours_start` time DEFAULT NULL COMMENT '免打扰开始时间',
  `quiet_hours_end` time DEFAULT NULL COMMENT '免打扰结束时间',
  `is_quiet_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用免打扰',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_type` (`user_id`,`notification_type`),
  KEY `idx_user_enabled` (`user_id`,`is_enabled`),
  KEY `idx_notification_type` (`notification_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='告警通知配置表';

-- ----------------------------
-- Table structure for alert_rules
-- ----------------------------
DROP TABLE IF EXISTS `alert_rules`;
CREATE TABLE `alert_rules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL COMMENT '用户ID，NULL表示系统告警规则',
  `rule_name` varchar(128) NOT NULL COMMENT '规则名称',
  `metric_type` varchar(50) NOT NULL COMMENT '指标类型:response_time,error_rate,rate_limit_hits',
  `operator` varchar(10) NOT NULL COMMENT '操作符:gt,lt,eq,gte,lte',
  `threshold` decimal(10,2) NOT NULL COMMENT '阈值',
  `threshold_unit` varchar(20) NOT NULL COMMENT '阈值单位：percent,count,ms',
  `time_window` int NOT NULL COMMENT '时间窗口(分钟)',
  `alert_level` tinyint(1) NOT NULL DEFAULT '2' COMMENT '告警级别：1-低，2-中，3-高，4-紧急',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1-删除 0-未删除',
  `description` varchar(500) DEFAULT NULL COMMENT '规则描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_metric_status` (`metric_type`,`status`),
  KEY `idx_alert_level` (`alert_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='告警规则表';

-- ----------------------------
-- Table structure for api_call_logs_tmplate
-- ----------------------------
DROP TABLE IF EXISTS `api_call_logs_tmplate`;
CREATE TABLE `api_call_logs_tmplate` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `access_key` varchar(64) NOT NULL DEFAULT '' COMMENT '访问密钥',
  `api_id` int NOT NULL COMMENT 'API ID',
  `api_name` varchar(128) NOT NULL DEFAULT '' COMMENT 'API名称',
  `method` varchar(10) NOT NULL COMMENT '请求类型',
  `api_path` varchar(512) NOT NULL DEFAULT '' COMMENT 'API路径',
  `request_id` varchar(64) NOT NULL COMMENT '请求ID',
  `request_ip` varchar(50) NOT NULL COMMENT '请求IP',
  `request_headers` text COMMENT '请求头，JSON格式',
  `request_body` longtext COMMENT '请求体',
  `response_code` int NOT NULL COMMENT '响应状态码',
  `response_body` longtext COMMENT '响应体',
  `response_time` int NOT NULL COMMENT '响应时间(毫秒)',
  `error_message` text COMMENT '错误信息',
  `call_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间（用于分区）',
  `record_time` datetime NOT NULL COMMENT '记录时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_request_id` (`request_id`) USING BTREE,
  KEY `idx_user_id` (`user_id`),
  KEY `idx_api_id` (`api_id`),
  KEY `idx_call_time` (`call_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='API调用日志表';

-- ----------------------------
-- Table structure for api_category
-- ----------------------------
DROP TABLE IF EXISTS `api_category`;
CREATE TABLE `api_category` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_dimension` enum('BUSINESS','FUNCTION') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类维度: BUSINESS-业务系统, FUNCTION-功能类型',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dimension_name` (`category_dimension`,`category_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='API分类表';

-- ----------------------------
-- Table structure for api_category_relation
-- ----------------------------
DROP TABLE IF EXISTS `api_category_relation`;
CREATE TABLE `api_category_relation` (
  `api_id` int NOT NULL COMMENT 'API ID',
  `category_id` int NOT NULL COMMENT '分类 ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='API分类关联关系表';

-- ----------------------------
-- Table structure for api_interfaces
-- ----------------------------
DROP TABLE IF EXISTS `api_interfaces`;
CREATE TABLE `api_interfaces` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `api_name` varchar(100) NOT NULL COMMENT 'API名称',
  `api_host` varchar(255) NOT NULL COMMENT 'API host',
  `api_path` varchar(255) NOT NULL COMMENT 'API路径',
  `method` varchar(10) NOT NULL COMMENT '请求类型：GET,POST,PUT,DELETE',
  `description` text COMMENT 'API描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-下线，1-上线',
  `request_schema` text COMMENT '请求参数Schema，JSON格式',
  `response_schema` text COMMENT '响应参数Schema，JSON格式',
  `request_example` text COMMENT '请求示例',
  `response_example` text COMMENT '响应示例',
  `created_by` varchar(64) NOT NULL COMMENT '创建人',
  `updated_by` varchar(64) NOT NULL COMMENT '更新人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_path_method` (`api_path`,`method`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='API接口表';

-- ----------------------------
-- Table structure for api_keys
-- ----------------------------
DROP TABLE IF EXISTS `api_keys`;
CREATE TABLE `api_keys` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '用户id',
  `key_name` varchar(100) NOT NULL COMMENT '密钥名称',
  `access_key` varchar(100) NOT NULL COMMENT 'AccessKey',
  `secret_key` varchar(100) NOT NULL COMMENT 'SecretKey',
  `permission_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '授权类型：1-全部API，2-部分API',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，2-禁用',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1-删除 0-未删除',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_access_key` (`access_key`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='API访问密钥表';

-- ----------------------------
-- Table structure for api_statistics_daily
-- ----------------------------
DROP TABLE IF EXISTS `api_statistics_daily`;
CREATE TABLE `api_statistics_daily` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `app_id` int NOT NULL COMMENT '用户ID',
  `api_id` int NOT NULL COMMENT 'API ID',
  `total_calls` int NOT NULL DEFAULT '0' COMMENT '总调用次数',
  `success_calls` int NOT NULL DEFAULT '0' COMMENT '成功调用次数',
  `fail_calls` int NOT NULL DEFAULT '0' COMMENT '失败调用次数',
  `avg_response_time` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平均响应时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_stat_app_api` (`stat_date`,`app_id`,`api_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='API调用每日统计表';

-- ----------------------------
-- Table structure for enterprise_auth
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_auth`;
CREATE TABLE `enterprise_auth` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '用户id',
  `company_name` varchar(100) NOT NULL COMMENT '企业名称',
  `contact_person` varchar(50) NOT NULL COMMENT '联系人',
  `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
  `contact_email` varchar(100) NOT NULL COMMENT '联系邮箱',
  `enterprise_type` varchar(20) NOT NULL COMMENT '企业类型',
  `business_license_number` varchar(50) NOT NULL COMMENT '统一社会信用代码',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='企业认证信息表';

-- ----------------------------
-- Table structure for key_api_permission
-- ----------------------------
DROP TABLE IF EXISTS `key_api_permission`;
CREATE TABLE `key_api_permission` (
  `key_id` int NOT NULL COMMENT 'key id',
  `api_id` int NOT NULL COMMENT 'api id',
  UNIQUE KEY `uk_key_api` (`key_id`,`api_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='密钥API权限关联表';

-- ----------------------------
-- Table structure for rate_limit_rules
-- ----------------------------
DROP TABLE IF EXISTS `rate_limit_rules`;
CREATE TABLE `rate_limit_rules` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `api_id` int NOT NULL COMMENT 'API ID',
  `time_window` enum('SECOND','MINUTE','HOUR','DAY') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '限流类型',
  `limit_count` int NOT NULL COMMENT '在该时间窗口内允许的最大请求次数',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='限流规则表';

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(64) NOT NULL COMMENT '用户名',
  `password` varchar(64) NOT NULL COMMENT '密码',
  `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1-启用 0-禁用',
  `created_by` varchar(64) NOT NULL COMMENT '创建人',
  `updated_by` varchar(64) NOT NULL COMMENT '更新人',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1-删除 0-未删除',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_modify_pwd_time` datetime DEFAULT NULL COMMENT '最后修改密码时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`contact_phone`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='企业用户表';

-- ----------------------------
-- Table structure for user_manager
-- ----------------------------
DROP TABLE IF EXISTS `user_manager`;
CREATE TABLE `user_manager` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户id',
  `manager_type` int NOT NULL COMMENT '管理员类型 1-超管理员 2-业务系统管理员',
  `is_delete` tinyint(1) NOT NULL COMMENT '是否删除 1-删除 0-未删除',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_manager_type` (`user_id`,`manager_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='管理员表';

-- ----------------------------
-- Table structure for user_quotas
-- ----------------------------
DROP TABLE IF EXISTS `user_quotas`;
CREATE TABLE `user_quotas` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '用户ID',
  `api_id` int DEFAULT NULL COMMENT '接口ID，NULL表示全局配额',
  `quota_limit` int NOT NULL COMMENT '配额限制',
  `quota_used` int NOT NULL DEFAULT '0' COMMENT '已使用配额',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_api_id` (`api_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户配额表';

SET FOREIGN_KEY_CHECKS = 1;
