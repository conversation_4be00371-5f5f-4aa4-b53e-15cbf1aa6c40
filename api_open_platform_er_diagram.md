# API开放平台数据库ER图

## 完整ER图（可编辑版本）

```mermaid
erDiagram
    %% ========================================
    %% 用户管理模块
    %% ========================================
    
    USER {
        int id PK "用户ID - 主键"
        varchar username "用户名 - 唯一标识"
        varchar password "密码 - 加密存储"
        varchar contact_phone UK "联系电话 - 唯一约束"
        varchar email UK "邮箱地址 - 唯一约束"
        tinyint status "状态 - 1启用/0禁用"
        varchar created_by "创建人"
        varchar updated_by "更新人"
        tinyint is_delete "删除标记 - 1删除/0正常"
        datetime created_time "创建时间"
        datetime updated_time "更新时间"
        datetime last_modify_pwd_time "最后修改密码时间"
    }
    
    ENTERPRISE_AUTH {
        int id PK "企业认证ID - 主键"
        int user_id UK "用户ID - 外键唯一"
        varchar company_name "企业名称"
        varchar contact_person "联系人姓名"
        varchar contact_phone "联系电话"
        varchar contact_email "联系邮箱"
        varchar enterprise_type "企业类型"
        varchar business_license_number "统一社会信用代码"
        text remark "备注信息"
    }
    
    USER_MANAGER {
        int id PK "管理员ID - 主键"
        varchar user_id "用户ID - 外键"
        int manager_type "管理员类型 - 1超管/2业务管理员"
        tinyint is_delete "删除标记 - 1删除/0正常"
        datetime created_time "创建时间"
    }
    
    %% ========================================
    %% API管理模块
    %% ========================================
    
    API_INTERFACES {
        int id PK "接口ID - 主键"
        varchar api_name "API名称"
        varchar api_host "API主机地址"
        varchar api_path "API路径"
        varchar method "请求方法 - GET/POST/PUT/DELETE"
        text description "API描述"
        tinyint status "状态 - 0下线/1上线"
        text request_schema "请求参数Schema - JSON格式"
        text response_schema "响应参数Schema - JSON格式"
        text request_example "请求示例"
        text response_example "响应示例"
        varchar created_by "创建人"
        varchar updated_by "更新人"
        datetime created_time "创建时间"
        datetime updated_time "更新时间"
    }
    
    API_CATEGORY {
        int id PK "分类ID - 主键"
        enum category_dimension "分类维度 - BUSINESS业务系统/FUNCTION功能类型"
        varchar category_name "分类名称"
    }
    
    API_CATEGORY_RELATION {
        int api_id "API ID - 外键"
        int category_id "分类ID - 外键"
    }
    
    %% ========================================
    %% 访问控制模块
    %% ========================================
    
    API_KEYS {
        int id PK "密钥ID - 主键"
        int user_id "用户ID - 外键"
        varchar key_name "密钥名称"
        varchar access_key UK "访问密钥 - 唯一约束"
        varchar secret_key "私钥"
        tinyint permission_type "授权类型 - 1全部API/2部分API"
        tinyint status "状态 - 1启用/2禁用"
        tinyint is_delete "删除标记 - 1删除/0正常"
        datetime created_time "创建时间"
    }
    
    KEY_API_PERMISSION {
        int key_id "密钥ID - 外键"
        int api_id "API ID - 外键"
    }
    
    USER_QUOTAS {
        int id PK "配额ID - 主键"
        int user_id "用户ID - 外键"
        int api_id "API ID - 外键，NULL表示全局配额"
        int quota_limit "配额限制"
        int quota_used "已使用配额"
        datetime created_time "创建时间"
        datetime updated_time "更新时间"
    }
    
    RATE_LIMIT_RULES {
        int id PK "限流规则ID - 主键"
        int api_id "API ID - 外键"
        enum time_window "时间窗口 - SECOND/MINUTE/HOUR/DAY"
        int limit_count "限制次数"
        datetime created_time "创建时间"
        datetime updated_time "更新时间"
    }
    
    %% ========================================
    %% 监控日志模块
    %% ========================================
    
    API_CALL_LOGS_TEMPLATE {
        bigint id PK "日志ID - 主键"
        int user_id "用户ID - 外键"
        varchar access_key "访问密钥"
        int api_id "API ID - 外键"
        varchar api_name "API名称"
        varchar method "请求方法"
        varchar api_path "API路径"
        varchar request_id UK "请求ID - 唯一约束"
        varchar request_ip "请求IP地址"
        text request_headers "请求头 - JSON格式"
        longtext request_body "请求体"
        int response_code "响应状态码"
        longtext response_body "响应体"
        int response_time "响应时间 - 毫秒"
        text error_message "错误信息"
        datetime call_time "调用时间 - 用于分区"
        datetime record_time "记录时间"
    }
    
    API_STATISTICS_DAILY {
        int id PK "统计ID - 主键"
        date stat_date "统计日期"
        int app_id "应用ID - 外键"
        int api_id "API ID - 外键"
        int total_calls "总调用次数"
        int success_calls "成功调用次数"
        int fail_calls "失败调用次数"
        decimal avg_response_time "平均响应时间"
    }
    
    %% ========================================
    %% 告警模块
    %% ========================================
    
    ALERT_RULES {
        bigint id PK "告警规则ID - 主键"
        int user_id "用户ID - 外键，NULL表示系统规则"
        varchar rule_name "规则名称"
        varchar metric_type "指标类型 - response_time/error_rate/rate_limit_hits"
        varchar operator "操作符 - gt/lt/eq/gte/lte"
        decimal threshold "阈值"
        varchar threshold_unit "阈值单位 - percent/count/ms"
        int time_window "时间窗口 - 分钟"
        tinyint alert_level "告警级别 - 1低/2中/3高/4紧急"
        tinyint status "状态 - 0禁用/1启用"
        tinyint is_delete "删除标记 - 1删除/0正常"
        varchar description "规则描述"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    ALERT_NOTIFICATION_CONFIGS {
        bigint id PK "通知配置ID - 主键"
        bigint user_id "用户ID - 外键"
        varchar notification_type "通知类型 - email/sms"
        tinyint is_enabled "是否启用该通知方式"
        tinyint high_level_enabled "是否接收高级告警"
        tinyint medium_level_enabled "是否接收中级告警"
        tinyint low_level_enabled "是否接收低级告警"
        tinyint critical_level_enabled "是否接收紧急告警"
        varchar notification_target "通知目标 - 邮箱地址/手机号"
        tinyint retry_times "重试次数"
        int retry_interval "重试间隔 - 秒"
        time quiet_hours_start "免打扰开始时间"
        time quiet_hours_end "免打扰结束时间"
        tinyint is_quiet_enabled "是否启用免打扰"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }
    
    %% ========================================
    %% 关系定义
    %% ========================================
    
    %% 用户相关关系
    USER ||--|| ENTERPRISE_AUTH : "1对1 - 企业认证"
    USER ||--o{ USER_MANAGER : "1对多 - 管理员权限"
    USER ||--o{ API_KEYS : "1对多 - 拥有密钥"
    USER ||--o{ USER_QUOTAS : "1对多 - 配额管理"
    USER ||--o{ API_CALL_LOGS_TEMPLATE : "1对多 - 调用日志"
    USER ||--o{ API_STATISTICS_DAILY : "1对多 - 统计数据"
    USER ||--o{ ALERT_RULES : "1对多 - 告警规则"
    USER ||--o{ ALERT_NOTIFICATION_CONFIGS : "1对多 - 通知配置"
    
    %% API相关关系
    API_INTERFACES ||--o{ API_CATEGORY_RELATION : "1对多 - 分类关联"
    API_CATEGORY ||--o{ API_CATEGORY_RELATION : "1对多 - 分类关联"
    
    %% 访问控制关系
    API_KEYS ||--o{ KEY_API_PERMISSION : "1对多 - 权限控制"
    API_INTERFACES ||--o{ KEY_API_PERMISSION : "1对多 - 权限控制"
    API_INTERFACES ||--o{ USER_QUOTAS : "1对多 - API配额"
    API_INTERFACES ||--o{ RATE_LIMIT_RULES : "1对多 - 限流规则"
    
    %% 监控日志关系
    API_INTERFACES ||--o{ API_CALL_LOGS_TEMPLATE : "1对多 - 调用日志"
    API_INTERFACES ||--o{ API_STATISTICS_DAILY : "1对多 - 统计数据"
```

## 表结构详细说明

### 核心实体说明

#### 1. 用户管理模块
- **USER**: 系统核心用户实体，支持软删除和审计字段
- **ENTERPRISE_AUTH**: 企业认证信息，与用户1:1关系
- **USER_MANAGER**: 管理员权限配置，支持多级管理员

#### 2. API管理模块  
- **API_INTERFACES**: API接口核心实体，包含完整的接口定义
- **API_CATEGORY**: 支持业务和功能两个维度的分类
- **API_CATEGORY_RELATION**: API与分类的多对多关系

#### 3. 访问控制模块
- **API_KEYS**: 用户访问凭证，支持全部或部分API权限
- **KEY_API_PERMISSION**: 密钥与API的精细化权限控制
- **USER_QUOTAS**: 支持全局和单API的配额管理
- **RATE_LIMIT_RULES**: 多时间窗口的限流规则

#### 4. 监控日志模块
- **API_CALL_LOGS_TEMPLATE**: 完整的API调用日志，支持分区存储
- **API_STATISTICS_DAILY**: 按日统计的API调用数据

#### 5. 告警模块
- **ALERT_RULES**: 灵活的告警规则配置
- **ALERT_NOTIFICATION_CONFIGS**: 多渠道通知配置，支持免打扰

### 设计亮点

1. **分区表设计**: 日志表支持按时间分区，便于大数据量管理
2. **软删除机制**: 关键表支持软删除，保证数据安全
3. **审计字段**: 完善的创建/更新时间和操作人记录
4. **唯一约束**: 合理的唯一约束设计，防止数据重复
5. **索引优化**: 针对查询场景的索引设计
6. **多维度分类**: 支持业务系统和功能类型双重分类
7. **精细化权限**: 密钥级别的API权限控制
8. **多级告警**: 四级告警等级和多渠道通知

## 编辑说明

此ER图使用Mermaid语法编写，可以通过以下方式编辑：

1. **在线编辑**: 使用Mermaid Live Editor (https://mermaid.live/)
2. **本地编辑**: 支持Mermaid的Markdown编辑器
3. **IDE插件**: VS Code、IntelliJ IDEA等IDE的Mermaid插件
4. **文档平台**: GitLab、GitHub等平台原生支持

### 编辑技巧

- 修改实体：直接编辑实体名称和字段
- 添加关系：使用 `||--o{` 等符号定义关系
- 调整布局：通过注释分组和空行优化布局
- 自定义样式：可添加CSS样式定义

