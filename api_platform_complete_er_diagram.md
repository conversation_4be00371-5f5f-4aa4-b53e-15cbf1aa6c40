# API开放平台完整数据库ER图

## 美观可编辑的完整ER图

```mermaid
erDiagram
    %% ========================================
    %% 用户管理模块 - User Management Module
    %% ========================================
    
    USER {
        int id PK "用户ID - 主键"
        varchar username "用户名 - 唯一标识"
        varchar password "密码 - 加密存储"
        varchar contact_phone UK "联系电话 - 唯一约束"
        varchar email UK "邮箱地址 - 唯一约束"
        tinyint status "状态 - 1启用/0禁用"
        varchar created_by "创建人"
        varchar updated_by "更新人"
        tinyint is_delete "删除标记 - 1删除/0正常"
        datetime created_time "创建时间"
        datetime updated_time "更新时间"
        datetime last_modify_pwd_time "最后修改密码时间"
    }
    
    ENTERPRISE_AUTH {
        int id PK "企业认证ID - 主键"
        int user_id UK "用户ID - 外键唯一"
        varchar company_name "企业名称"
        varchar contact_person "联系人姓名"
        varchar contact_phone "联系电话"
        varchar contact_email "联系邮箱"
        varchar enterprise_type "企业类型"
        varchar business_license_number "统一社会信用代码"
        text remark "备注信息"
    }
    
    USER_MANAGER {
        int id PK "管理员ID - 主键"
        varchar user_id "用户ID - 外键"
        int manager_type "管理员类型 - 1超管/2业务管理员"
        tinyint is_delete "删除标记 - 1删除/0正常"
        datetime created_time "创建时间"
    }
    
    %% ========================================
    %% API管理模块 - API Management Module
    %% ========================================
    
    API_INTERFACES {
        int id PK "接口ID - 主键"
        varchar api_name "API名称"
        varchar api_path "API路径"
        varchar method "请求方法 - GET/POST/PUT/DELETE"
        text description "API描述"
        tinyint status "状态 - 0下线/1上线"
        text request_schema "请求参数Schema - JSON格式"
        text response_schema "响应参数Schema - JSON格式"
        text request_example "请求示例"
        text response_example "响应示例"
        varchar created_by "创建人"
        varchar updated_by "更新人"
        datetime created_time "创建时间"
        datetime updated_time "更新时间"
    }
    
    API_CATEGORY {
        int id PK "分类ID - 主键"
        enum category_dimension "分类维度 - BUSINESS业务系统/FUNCTION功能类型"
        varchar category_name "分类名称"
    }
    
    API_CATEGORY_RELATION {
        int api_id "API ID - 外键"
        int category_id "分类ID - 外键"
    }
    
    %% ========================================
    %% 访问控制模块 - Access Control Module
    %% ========================================
    
    API_KEYS {
        int id PK "密钥ID - 主键"
        int user_id "用户ID - 外键"
        varchar key_name "密钥名称"
        varchar access_key UK "访问密钥 - 唯一约束"
        varchar secret_key "私钥"
        tinyint permission_type "授权类型 - 1全部API/2部分API"
        tinyint status "状态 - 1启用/2禁用"
        tinyint is_delete "删除标记 - 1删除/0正常"
        datetime created_time "创建时间"
    }
    
    KEY_API_PERMISSION {
        int key_id "密钥ID - 外键"
        int api_id "API ID - 外键"
    }
    
    USER_QUOTAS {
        int id PK "配额ID - 主键"
        int user_id "用户ID - 外键"
        int api_id "API ID - 外键，NULL表示全局配额"
        int quota_limit "配额限制"
        int quota_used "已使用配额"
        datetime created_time "创建时间"
        datetime updated_time "更新时间"
    }
    
    RATE_LIMIT_RULES {
        int id PK "限流规则ID - 主键"
        int api_id "API ID - 外键"
        enum time_window "时间窗口 - SECOND/MINUTE/HOUR/DAY"
        int limit_count "限制次数"
        datetime created_time "创建时间"
        datetime updated_time "更新时间"
    }
    
    %% ========================================
    %% 监控日志模块 - Monitoring & Logging Module
    %% ========================================
    
    API_CALL_LOGS_TEMPLATE {
        bigint id PK "日志ID - 主键"
        int user_id "用户ID - 外键"
        varchar access_key "访问密钥"
        int api_id "API ID - 外键"
        varchar api_name "API名称"
        varchar method "请求方法"
        varchar api_path "API路径"
        varchar request_id UK "请求ID - 唯一约束"
        varchar request_ip "请求IP地址"
        text request_headers "请求头 - JSON格式"
        longtext request_body "请求体"
        int response_code "响应状态码"
        longtext response_body "响应体"
        int response_time "响应时间 - 毫秒"
        text error_message "错误信息"
        datetime call_time "调用时间 - 用于分区"
        datetime record_time "记录时间"
    }
    
    API_STATISTICS_DAILY {
        int id PK "统计ID - 主键"
        date stat_date "统计日期"
        int app_id "应用ID - 外键"
        int api_id "API ID - 外键"
        int total_calls "总调用次数"
        int success_calls "成功调用次数"
        int fail_calls "失败调用次数"
        decimal avg_response_time "平均响应时间"
    }
    
    %% ========================================
    %% 告警模块 - Alert Module
    %% ========================================
    
    ALERT_RULES {
        bigint id PK "告警规则ID - 主键"
        int user_id "用户ID - 外键，NULL表示系统规则"
        varchar rule_name "规则名称"
        varchar metric_type "指标类型 - response_time/error_rate/rate_limit_hits"
        varchar operator "操作符 - gt/lt/eq/gte/lte"
        decimal threshold "阈值"
        varchar threshold_unit "阈值单位 - percent/count/ms"
        int time_window "时间窗口 - 分钟"
        tinyint alert_level "告警级别 - 1低/2中/3高/4紧急"
        tinyint status "状态 - 0禁用/1启用"
        tinyint is_delete "删除标记 - 1删除/0正常"
        varchar description "规则描述"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    ALERT_NOTIFICATION_CONFIGS {
        bigint id PK "通知配置ID - 主键"
        bigint user_id "用户ID - 外键"
        varchar notification_type "通知类型 - email/sms"
        tinyint is_enabled "是否启用该通知方式"
        tinyint high_level_enabled "是否接收高级告警"
        tinyint medium_level_enabled "是否接收中级告警"
        tinyint low_level_enabled "是否接收低级告警"
        tinyint critical_level_enabled "是否接收紧急告警"
        varchar notification_target "通知目标 - 邮箱地址/手机号"
        tinyint retry_times "重试次数"
        int retry_interval "重试间隔 - 秒"
        time quiet_hours_start "免打扰开始时间"
        time quiet_hours_end "免打扰结束时间"
        tinyint is_quiet_enabled "是否启用免打扰"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }
    
    %% ========================================
    %% 关系定义 - Relationships
    %% ========================================
    
    %% 用户相关关系 - User Related Relationships
    USER ||--|| ENTERPRISE_AUTH : "1对1 - 企业认证"
    USER ||--o{ USER_MANAGER : "1对多 - 管理员权限"
    USER ||--o{ API_KEYS : "1对多 - 拥有密钥"
    USER ||--o{ USER_QUOTAS : "1对多 - 配额管理"
    USER ||--o{ API_CALL_LOGS_TEMPLATE : "1对多 - 调用日志"
    USER ||--o{ API_STATISTICS_DAILY : "1对多 - 统计数据"
    USER ||--o{ ALERT_RULES : "1对多 - 告警规则"
    USER ||--o{ ALERT_NOTIFICATION_CONFIGS : "1对多 - 通知配置"
    
    %% API相关关系 - API Related Relationships
    API_INTERFACES ||--o{ API_CATEGORY_RELATION : "1对多 - 分类关联"
    API_CATEGORY ||--o{ API_CATEGORY_RELATION : "1对多 - 分类关联"
    
    %% 访问控制关系 - Access Control Relationships
    API_KEYS ||--o{ KEY_API_PERMISSION : "1对多 - 权限控制"
    API_INTERFACES ||--o{ KEY_API_PERMISSION : "1对多 - 权限控制"
    API_INTERFACES ||--o{ USER_QUOTAS : "1对多 - API配额"
    API_INTERFACES ||--o{ RATE_LIMIT_RULES : "1对多 - 限流规则"
    
    %% 监控日志关系 - Monitoring & Logging Relationships
    API_INTERFACES ||--o{ API_CALL_LOGS_TEMPLATE : "1对多 - 调用日志"
    API_INTERFACES ||--o{ API_STATISTICS_DAILY : "1对多 - 统计数据"
```

## 表结构详细说明

### 🏢 用户管理模块 (User Management Module)

#### 1. USER - 用户表
**核心用户实体，系统的基础表**
- **主要功能**: 存储企业用户的基本信息
- **关键字段**:
  - `username`: 用户名，用于登录
  - `contact_phone` & `email`: 双重唯一约束，支持手机号或邮箱登录
  - `status`: 用户状态控制，支持启用/禁用
  - `is_delete`: 软删除标记，保证数据安全
  - `last_modify_pwd_time`: 密码安全策略支持

#### 2. ENTERPRISE_AUTH - 企业认证表
**企业认证信息，与用户1:1关系**
- **主要功能**: 存储企业认证相关信息
- **关键字段**:
  - `business_license_number`: 统一社会信用代码，企业唯一标识
  - `enterprise_type`: 企业类型分类
  - `contact_person`: 企业联系人信息

#### 3. USER_MANAGER - 管理员表
**管理员权限配置**
- **主要功能**: 管理系统管理员权限
- **管理员类型**:
  - 1: 超级管理员 - 拥有所有权限
  - 2: 业务系统管理员 - 拥有业务相关权限

### 🔌 API管理模块 (API Management Module)

#### 4. API_INTERFACES - API接口表
**系统核心表，定义所有可用的API接口**
- **主要功能**: 存储API接口的完整定义
- **关键字段**:
  - `api_path` + `method`: 唯一标识一个API
  - `request_schema` & `response_schema`: JSON Schema格式的参数定义
  - `request_example` & `response_example`: 接口使用示例
  - `status`: 接口上下线状态控制

#### 5. API_CATEGORY - API分类表
**支持多维度的API分类管理**
- **分类维度**:
  - `BUSINESS`: 按业务系统分类 (如CRM、ERP、财务等)
  - `FUNCTION`: 按功能类型分类 (如查询、更新、删除等)

#### 6. API_CATEGORY_RELATION - API分类关联表
**API与分类的多对多关系表**
- **设计优势**: 一个API可以属于多个分类，便于多维度管理

### 🔐 访问控制模块 (Access Control Module)

#### 7. API_KEYS - API密钥表
**用户访问凭证管理**
- **主要功能**: 管理用户的API访问密钥
- **关键字段**:
  - `access_key`: 公开的访问密钥，用于标识用户
  - `secret_key`: 私密密钥，用于签名验证
  - `permission_type`: 权限类型 (1-全部API, 2-部分API)

#### 8. KEY_API_PERMISSION - 密钥API权限表
**精细化权限控制**
- **主要功能**: 当permission_type=2时，定义密钥可访问的具体API
- **设计优势**: 支持密钥级别的精细化权限控制

#### 9. USER_QUOTAS - 用户配额表
**API调用配额管理**
- **配额类型**:
  - 全局配额: `api_id` 为 NULL，限制用户总调用量
  - 单API配额: 指定 `api_id`，限制特定API调用量
- **实时跟踪**: `quota_used` 字段实时更新使用量

#### 10. RATE_LIMIT_RULES - 限流规则表
**API限流保护**
- **时间窗口**: SECOND/MINUTE/HOUR/DAY 四种粒度
- **限流策略**: 在指定时间窗口内限制最大请求次数
- **保护机制**: 防止API被恶意调用或过度使用

### 📊 监控日志模块 (Monitoring & Logging Module)

#### 11. API_CALL_LOGS_TEMPLATE - API调用日志表
**完整的API调用记录**
- **设计特点**:
  - 支持分区表设计，按 `call_time` 进行时间分区
  - 记录完整的请求/响应信息，便于问题排查
  - `request_id` 唯一约束，支持请求链路追踪
- **性能优化**:
  - 索引设计: `idx_user_id`, `idx_api_id`, `idx_call_time`
  - 分区策略: 按月或按日分区，便于历史数据管理

#### 12. API_STATISTICS_DAILY - API统计表
**按日统计的API调用数据**
- **统计维度**: 用户 + API + 日期
- **统计指标**: 总调用数、成功数、失败数、平均响应时间
- **应用场景**: 报表生成、趋势分析、性能监控

### 🚨 告警模块 (Alert Module)

#### 13. ALERT_RULES - 告警规则表
**灵活的告警规则配置**
- **监控指标**:
  - `response_time`: 响应时间监控
  - `error_rate`: 错误率监控
  - `rate_limit_hits`: 限流命中监控
- **告警级别**: 1-低, 2-中, 3-高, 4-紧急
- **规则配置**: 支持 gt/lt/eq/gte/lte 多种比较操作符

#### 14. ALERT_NOTIFICATION_CONFIGS - 告警通知配置表
**多渠道告警通知管理**
- **通知渠道**: email/sms 等多种方式
- **分级通知**: 可配置不同级别告警的接收策略
- **免打扰功能**: 支持免打扰时间段设置
- **重试机制**: 配置重试次数和间隔，确保通知送达

## 🎨 设计亮点

### 1. 数据安全性
- **软删除机制**: 关键表支持软删除，防止误删数据
- **审计字段**: 完整的创建/更新时间和操作人记录
- **唯一约束**: 合理的唯一约束设计，防止数据重复

### 2. 性能优化
- **分区表设计**: 日志表支持按时间分区，提升查询性能
- **索引策略**: 针对常用查询场景设计索引
- **数据类型优化**: 合理选择数据类型，节省存储空间

### 3. 扩展性设计
- **多维度分类**: 支持业务和功能双重分类维度
- **灵活权限控制**: 支持全局和精细化权限管理
- **可配置告警**: 支持多种监控指标和通知方式

### 4. 业务完整性
- **完整的用户生命周期**: 注册、认证、权限管理
- **全面的API管理**: 定义、分类、权限、监控
- **完善的监控体系**: 日志记录、统计分析、告警通知

## 📝 编辑使用说明

### 在线编辑工具
1. **Mermaid Live Editor**: https://mermaid.live/
2. **Draw.io**: 支持导入Mermaid格式
3. **Lucidchart**: 专业的ER图绘制工具

### 本地编辑环境
1. **VS Code**: 安装Mermaid Preview插件
2. **IntelliJ IDEA**: 安装Mermaid插件
3. **Typora**: 原生支持Mermaid渲染

### 编辑技巧
- **添加实体**: 复制现有实体格式，修改名称和字段
- **修改关系**: 使用 `||--o{` (一对多), `||--||` (一对一), `}o--o{` (多对多)
- **调整布局**: 通过注释分组和空行优化视觉效果
- **自定义样式**: 可在图表末尾添加CSS样式定义

### 版本控制
- 建议使用Git管理ER图版本
- 每次重大修改添加版本注释
- 保持与实际数据库结构同步更新
