<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>忘记密码 - 企业API开放平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .login-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .form-container {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
    </style>
</head>
<body class="login-bg min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <!-- Logo和标题 -->
        <div class="text-center mb-8">
            <div class="flex justify-center mb-4">
                <img class="h-12 w-12" src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=48&h=48&fit=crop&crop=center" alt="Logo">
            </div>
            <h2 class="text-3xl font-bold text-white mb-2">重置密码</h2>
            <p class="text-white/80">请输入您的注册信息以重置密码</p>
        </div>

        <!-- 忘记密码表单 -->
        <div class="form-container rounded-2xl shadow-2xl p-8">
            <form class="space-y-6">
                
                <div class="mb-4">
                    <label for="forgot-phone" class="block text-sm font-medium text-gray-700 mb-2">
                        账号
                    </label>
                    <input id="forgot-phone" name="forgot-phone" type="tel" 
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="请输入手机号或邮箱">
                </div>
                
                <div class="flex space-x-3 mb-4">
                    <div class="flex-1">
                        <label for="verification-code" class="block text-sm font-medium text-gray-700 mb-2">
                            验证码
                        </label>
                        <input id="verification-code" name="verification-code" type="text" 
                               class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                               placeholder="请输入验证码">
                    </div>
                    <button type="button" 
                            class="px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 mt-6">
                        获取验证码
                    </button>
                </div>
                
                <div class="mb-4">
                    <label for="new-password" class="block text-sm font-medium text-gray-700 mb-2">
                        新密码
                    </label>
                    <input id="new-password" name="new-password" type="password" 
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="请输入新密码">
                </div>
                
                <div class="flex space-x-3">
                    <button type="submit" 
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-key text-indigo-500 group-hover:text-indigo-400"></i>
                        </span>
                        重置密码
                    </button>
                </div>
            </form>

            <!-- 返回登录链接 -->
            <div class="mt-6 text-center">
                <p class="text-sm text-gray-600">
                    <a href="login.html" class="font-medium text-indigo-600 hover:text-indigo-500 flex items-center justify-center">
                        <i class="fas fa-arrow-left mr-2"></i>
                        返回登录
                    </a>
                </p>
            </div>
        </div>

        <!-- 安全提示 -->
        <div class="mt-8 text-center">
            <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div class="flex items-center justify-center text-white/80 text-sm">
                    <i class="fas fa-shield-alt mr-2"></i>
                    <span>您的数据受到256位SSL加密保护</span>
                </div>
            </div>
        </div>

        <!-- 帮助链接 -->
        <div class="mt-6 text-center">
            <div class="flex justify-center space-x-6 text-white/60 text-sm">
                <a href="#" class="hover:text-white">帮助中心</a>
                <a href="#" class="hover:text-white">隐私政策</a>
                <a href="#" class="hover:text-white">服务条款</a>
            </div>
        </div>
    </div>

    <script>
        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('forgot-email').value;
            const phone = document.getElementById('forgot-phone').value;
            const code = document.getElementById('verification-code').value;
            const newPassword = document.getElementById('new-password').value;
            
            if (!email || !phone || !code || !newPassword) {
                alert('请填写所有必填字段');
                return;
            }
            
            // 模拟提交过程
            const button = document.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>提交中...';
            button.disabled = true;
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                alert('密码重置成功！');
                window.location.href = 'login.html';
            }, 2000);
        });

        // 输入框焦点效果
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('ring-2', 'ring-indigo-500');
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('ring-2', 'ring-indigo-500');
            });
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 企业API开放平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .login-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .form-container {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
    </style>
</head>
<body class="login-bg min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <!-- Logo和标题 -->
        <div class="text-center mb-8">
            <div class="flex justify-center mb-4">
                <img class="h-12 w-12" src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=48&h=48&fit=crop&crop=center" alt="Logo">
            </div>
            <h2 class="text-3xl font-bold text-white mb-2">欢迎回来</h2>
            <p class="text-white/80">登录您的企业API开放平台账户</p>
        </div>

        <!-- 登录表单 -->
        <div class="form-container rounded-2xl shadow-2xl p-8">
            <form class="space-y-6">
                <!-- 账号 -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        账号
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                        <input id="email" name="email" type="text" required 
                               class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                               placeholder="请输入手机号或邮箱">
                    </div>
                </div>

                <!-- 密码 -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        密码
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input id="password" name="password" type="password" required 
                               class="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                               placeholder="请输入密码">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="togglePassword()">
                                <i id="password-icon" class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 记住我和忘记密码 -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input id="remember-me" name="remember-me" type="checkbox" 
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label for="remember-me" class="ml-2 block text-sm text-gray-700">
                            记住我
                        </label>
                    </div>
                    <div class="text-sm">
                        <a href="forgot-password.html" class="font-medium text-indigo-600 hover:text-indigo-500">
                            忘记密码？
                        </a>
                    </div>
                </div>

                <!-- 登录按钮 -->
                <div>
                    <button type="submit" 
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-sign-in-alt text-indigo-500 group-hover:text-indigo-400"></i>
                        </span>
                        登录
                    </button>
                </div>

            </form>

            <!-- 注册链接 -->
            <div class="mt-6 text-center">
                <p class="text-sm text-gray-600">
                    还没有账号？请联系管理员
                    <!--<a href="register.html" class="font-medium text-indigo-600 hover:text-indigo-500">
                        立即注册
                    </a>-->
                </p>
            </div>
        </div>

        <!-- 安全提示 -->
        <div class="mt-8 text-center">
            <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div class="flex items-center justify-center text-white/80 text-sm">
                    <i class="fas fa-shield-alt mr-2"></i>
                    <span>您的数据受到256位SSL加密保护</span>
                </div>
            </div>
        </div>

        <!-- 帮助链接 -->
        <div class="mt-6 text-center">
            <div class="flex justify-center space-x-6 text-white/60 text-sm">
                <a href="#" class="hover:text-white">帮助中心</a>
                <a href="#" class="hover:text-white">隐私政策</a>
                <a href="#" class="hover:text-white">服务条款</a>
            </div>
        </div>
    </div>

    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordIcon = document.getElementById('password-icon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }

        function showForgotPassword() {
            event.preventDefault();
            document.querySelector('form .space-y-6').classList.add('hidden');
            document.getElementById('forgot-password-form').classList.remove('hidden');
        }

        function hideForgotPassword() {
            document.querySelector('form .space-y-6').classList.remove('hidden');
            document.getElementById('forgot-password-form').classList.add('hidden');
        }

        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                alert('请填写所有必填字段');
                return;
            }
            
            // 模拟登录过程
            const button = document.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>登录中...';
            button.disabled = true;
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                alert('登录成功！');
            }, 2000);
        });

        // 输入框焦点效果
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('ring-2', 'ring-indigo-500');
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('ring-2', 'ring-indigo-500');
            });
        });
    </script>
</body>
</html>