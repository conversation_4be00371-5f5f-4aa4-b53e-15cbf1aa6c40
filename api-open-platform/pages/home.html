<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业API开放平台 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        .feature-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <img class="h-8 w-8" src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=32&h=32&fit=crop&crop=center" alt="Logo">
                    </div>
                    <div class="ml-3">
                        <span class="text-xl font-bold text-gray-800">企业API开放平台</span>
                    </div>
                </div>
                <!--<div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="#" class="text-gray-900 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                        <a href="#" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">API文档</a>
                        <a href="#" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">支持</a>
                    </div>
                </div>-->
                <div class="flex items-center space-x-4">
                    <button class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">登录</button>
<!--                    <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium">注册</button>-->
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-gradient text-white">
        <div class="max-w-7xl mx-auto px-4 py-24">
            <div class="text-center">
                <h1 class="text-5xl font-bold mb-6">企业API开放平台</h1>
                <p class="text-xl mb-8 max-w-3xl mx-auto">统一管理公司各业务系统API接口，为内部开发团队提供标准化、高效的API服务，促进系统间数据互通与业务协同</p>
                <div class="flex justify-center space-x-4">
                    <button class="bg-white text-indigo-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold text-lg">
                        <i class="fas fa-rocket mr-2"></i>立即接入
                    </button>
                    <button class="border-2 border-white text-white hover:bg-white hover:text-indigo-600 px-8 py-3 rounded-lg font-semibold text-lg">
                        <i class="fas fa-book mr-2"></i>查看文档
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- 统计数据 -->
    <section class="bg-white py-16">
        <div class="max-w-7xl mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="text-4xl font-bold text-indigo-600 mb-2">120+</div>
                    <div class="text-gray-600">业务系统接口</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-indigo-600 mb-2">50+</div>
                    <div class="text-gray-600">内部开发团队</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-indigo-600 mb-2">99.9%</div>
                    <div class="text-gray-600">服务可用性</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-indigo-600 mb-2">500K+</div>
                    <div class="text-gray-600">日调用量</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 平台优势 -->
    <section class="bg-gray-50 py-20">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">平台优势</h2>
                <p class="text-xl text-gray-600">为企业内部开发提供统一、高效的API服务管理平台</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- 统一接入 -->
                <div class="bg-white rounded-xl p-8 card-hover">
                    <div class="feature-icon w-16 h-16 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-plug text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">统一接入</h3>
                    <p class="text-gray-600 mb-4">整合公司各业务系统API，提供统一的接入标准和调用方式，简化系统间集成复杂度</p>
                    <ul class="text-sm text-gray-500 space-y-2">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>标准化接口规范</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>统一认证机制</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>简化集成流程</li>
                    </ul>
                </div>

                <!-- 高效协同 -->
                <div class="bg-white rounded-xl p-8 card-hover">
                    <div class="feature-icon w-16 h-16 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-users text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">高效协同</h3>
                    <p class="text-gray-600 mb-4">促进各业务团队间的协作，通过API共享实现数据互通和业务流程优化</p>
                    <ul class="text-sm text-gray-500 space-y-2">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>跨部门数据共享</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>业务流程打通</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>团队协作提升</li>
                    </ul>
                </div>

                <!-- 安全可靠 -->
                <div class="bg-white rounded-xl p-8 card-hover">
                    <div class="feature-icon w-16 h-16 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-shield-alt text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">安全可靠</h3>
                    <p class="text-gray-600 mb-4">企业级安全保障，完善的权限控制和访问管理，确保数据安全和系统稳定</p>
                    <ul class="text-sm text-gray-500 space-y-2">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>权限精细控制</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>数据安全保护</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>高可用架构</li>
                    </ul>
                </div>

                <!-- 智能监控 -->
                <div class="bg-white rounded-xl p-8 card-hover">
                    <div class="feature-icon w-16 h-16 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-chart-line text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">智能监控</h3>
                    <p class="text-gray-600 mb-4">全方位监控API调用情况，智能告警机制，实时掌握系统运行状态</p>
                    <ul class="text-sm text-gray-500 space-y-2">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>实时性能监控</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>智能告警通知</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>数据分析报表</li>
                    </ul>
                </div>

                <!-- 流量控制 -->
                <div class="bg-white rounded-xl p-8 card-hover">
                    <div class="feature-icon w-16 h-16 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-tachometer-alt text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">流量控制</h3>
                    <p class="text-gray-600 mb-4">智能流量管理，按业务优先级和系统负载动态调节，保障核心业务稳定运行</p>
                    <ul class="text-sm text-gray-500 space-y-2">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>智能限流策略</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>业务优先级控制</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>负载均衡调度</li>
                    </ul>
                </div>

                <!-- 开发支持 -->
                <div class="bg-white rounded-xl p-8 card-hover">
                    <div class="feature-icon w-16 h-16 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-code text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">开发支持</h3>
                    <p class="text-gray-600 mb-4">完善的开发文档和工具支持，多语言SDK，助力开发团队快速集成</p>
                    <ul class="text-sm text-gray-500 space-y-2">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>详细API文档</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>多语言SDK</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>代码示例库</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- 业务系统接口 -->
    <section class="bg-white py-20">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">业务系统接口</h2>
                <p class="text-xl text-gray-600">整合公司各业务系统，提供统一的API服务接入</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 text-center">
                    <i class="fas fa-users text-4xl text-indigo-600 mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">用户管理系统</h3>
                    <p class="text-sm text-gray-600">用户信息、权限管理、组织架构</p>
                </div>
                <div class="bg-gradient-to-br from-green-50 to-emerald-100 rounded-xl p-6 text-center">
                    <i class="fas fa-shopping-cart text-4xl text-emerald-600 mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">订单管理系统</h3>
                    <p class="text-sm text-gray-600">订单处理、状态跟踪、支付管理</p>
                </div>
                <div class="bg-gradient-to-br from-purple-50 to-violet-100 rounded-xl p-6 text-center">
                    <i class="fas fa-warehouse text-4xl text-violet-600 mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">库存管理系统</h3>
                    <p class="text-sm text-gray-600">库存查询、出入库、盘点管理</p>
                </div>
                <div class="bg-gradient-to-br from-orange-50 to-red-100 rounded-xl p-6 text-center">
                    <i class="fas fa-chart-bar text-4xl text-red-600 mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">财务管理系统</h3>
                    <p class="text-sm text-gray-600">财务数据、报表生成、成本分析</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="hero-gradient text-white py-20">
        <div class="max-w-4xl mx-auto px-4 text-center">
            <h2 class="text-4xl font-bold mb-6">准备好开始了吗？</h2>
            <p class="text-xl mb-8">立即接入企业API开放平台，享受统一、高效的API服务</p>
            <div class="flex justify-center space-x-4">
                <button class="bg-white text-indigo-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold text-lg">
                    立即接入
                </button>
                <button class="border-2 border-white text-white hover:bg-white hover:text-indigo-600 px-8 py-3 rounded-lg font-semibold text-lg">
                    联系管理员
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">产品</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">API文档</a></li>
                        <li><a href="#" class="hover:text-white">价格方案</a></li>
                        <li><a href="#" class="hover:text-white">SDK下载</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">开发者</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">快速开始</a></li>
                        <li><a href="#" class="hover:text-white">代码示例</a></li>
                        <li><a href="#" class="hover:text-white">社区论坛</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">支持</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">帮助中心</a></li>
                        <li><a href="#" class="hover:text-white">联系我们</a></li>
                        <li><a href="#" class="hover:text-white">状态页面</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">公司</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">关于我们</a></li>
                        <li><a href="#" class="hover:text-white">隐私政策</a></li>
                        <li><a href="#" class="hover:text-white">服务条款</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 企业API开放平台. 保留所有权利.</p>
            </div>
        </div>
    </footer>
</body>
</html>
