<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 企业API开放平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .register-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .form-container {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .tab-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body class="register-bg min-h-screen py-8">
    <div class="max-w-2xl w-full mx-auto px-4">
        <!-- Logo和标题 -->
        <div class="text-center mb-8">
            <div class="flex justify-center mb-4">
                <img class="h-12 w-12" src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=48&h=48&fit=crop&crop=center" alt="Logo">
            </div>
            <h2 class="text-3xl font-bold text-white mb-2">创建账户</h2>
            <p class="text-white/80">加入企业API开放平台，享受统一高效的API服务</p>
        </div>

        <!-- 注册表单 -->
        <div class="form-container rounded-2xl shadow-2xl p-8">


            <form class="space-y-6">
                <!-- 个人用户表单 -->
                <div id="personal-form">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 用户名 -->
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                                用户名 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user text-gray-400"></i>
                                </div>
                                <input id="username" name="username" type="text" required
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                       placeholder="请输入用户名">
                            </div>
                        </div>

                        <!-- 邮箱 -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                邮箱地址 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-envelope text-gray-400"></i>
                                </div>
                                <input id="email" name="email" type="email" required
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                       placeholder="请输入邮箱地址">
                            </div>
                        </div>

                        <!-- 密码 -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                密码 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-gray-400"></i>
                                </div>
                                <input id="password" name="password" type="password" required
                                       class="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                       placeholder="请输入密码">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="togglePassword('password')">
                                        <i id="password-icon" class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="mt-1 text-xs text-gray-500">
                                密码长度至少8位，包含字母和数字
                            </div>
                        </div>

                        <!-- 确认密码 -->
                        <div>
                            <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-2">
                                确认密码 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-gray-400"></i>
                                </div>
                                <input id="confirm-password" name="confirm-password" type="password" required
                                       class="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                       placeholder="请再次输入密码">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="togglePassword('confirm-password')">
                                        <i id="confirm-password-icon" class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 手机号 -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                            手机号码
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-phone text-gray-400"></i>
                            </div>
                            <input id="phone" name="phone" type="tel"
                                   class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                   placeholder="请输入手机号码">
                        </div>
                    </div>
                </div>

                <!-- 企业认证提示 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">企业认证说明</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <p>注册后可在个人中心进行企业认证，认证通过后可享受企业级服务支持。</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 验证码 -->
                <div>
                    <label for="captcha" class="block text-sm font-medium text-gray-700 mb-2">
                        验证码 <span class="text-red-500">*</span>
                    </label>
                    <div class="flex space-x-3">
                        <div class="flex-1 relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-shield-alt text-gray-400"></i>
                            </div>
                            <input id="captcha" name="captcha" type="text" required
                                   class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                   placeholder="请输入验证码">
                        </div>
                        <button type="button"
                                class="px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            获取验证码
                        </button>
                    </div>
                </div>

                <!-- 服务条款 -->
                <div class="flex items-start">
                    <div class="flex items-center h-5">
                        <input id="agree-terms" name="agree-terms" type="checkbox" required
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="agree-terms" class="text-gray-700">
                            我已阅读并同意
                            <a href="#" class="text-indigo-600 hover:text-indigo-500">《用户服务协议》</a>
                            和
                            <a href="#" class="text-indigo-600 hover:text-indigo-500">《隐私政策》</a>
                        </label>
                    </div>
                </div>

                <!-- 注册按钮 -->
                <div>
                    <button type="submit"
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-user-plus text-indigo-500 group-hover:text-indigo-400"></i>
                        </span>
                        创建账户
                    </button>
                </div>
            </form>

            <!-- 登录链接 -->
            <div class="mt-6 text-center">
                <p class="text-sm text-gray-600">
                    已有账户？
                    <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500">
                        立即登录
                    </a>
                </p>
            </div>
        </div>
    </div>

    <script>

        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const passwordIcon = document.getElementById(inputId + '-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }

        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();

            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            const agreeTerms = document.getElementById('agree-terms').checked;

            if (!agreeTerms) {
                alert('请同意用户服务协议和隐私政策');
                return;
            }

            if (password !== confirmPassword) {
                alert('两次输入的密码不一致');
                return;
            }

            if (password.length < 8) {
                alert('密码长度至少8位');
                return;
            }

            // 模拟注册过程
            const button = document.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;

            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>注册中...';
            button.disabled = true;

            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                alert('注册成功！请查收邮箱验证邮件。');
            }, 2000);
        });
    </script>
</body>
</html>
