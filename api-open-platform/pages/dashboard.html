<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制台 - 企业API开放平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .metric-card {
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <img class="h-8 w-8" src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=32&h=32&fit=crop&crop=center" alt="Logo">
                    <span class="ml-3 text-xl font-bold text-gray-800">企业API开放平台</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <button class="p-2 text-gray-400 hover:text-gray-600">
                            <i class="fas fa-bell text-lg"></i>
                            <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400"></span>
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="用户头像">
                        <span class="text-sm font-medium text-gray-700">张开发者</span>
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- 侧边栏 -->
        <div class="w-64 bg-white shadow-sm h-screen">
            <div class="p-6">
                <nav class="space-y-2">
                    <a href="#" class="gradient-bg text-white group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        控制台
                    </a>
                    <a href="#" class="text-gray-600 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-book mr-3"></i>
                        API文档
                    </a>

                    <a href="#" class="text-gray-600 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-key mr-3"></i>
                        密钥管理
                    </a>
                    <a href="#" class="text-gray-600 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-list-alt mr-3"></i>
                        调用日志
                    </a>
                    <a href="#" class="text-gray-600 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-chart-line mr-3"></i>
                        监控告警
                    </a>
                    <a href="#" class="text-gray-600 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-user mr-3"></i>
                        个人中心
                    </a>
                    <a href="#" class="text-gray-600 hover:bg-gray-50 group flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i class="fas fa-cog mr-3"></i>
                        系统管理
                    </a>
                </nav>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="flex-1 p-8">
            <!-- 页面标题 -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">控制台</h1>
                <p class="mt-2 text-gray-600">欢迎回来，这里是您的API使用概览</p>
            </div>

            <!-- 核心指标卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- 今日调用量 -->
                <div class="bg-white rounded-lg p-6 card-shadow metric-card">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-bar text-blue-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">今日调用量</p>
                            <p class="text-2xl font-bold text-gray-900">1,247</p>
                            <!--<p class="text-sm text-green-600">
                                <i class="fas fa-arrow-up mr-1"></i>
                                +12.5%
                            </p>-->
                        </div>
                    </div>
                </div>

                <!-- 活跃接口 -->
                <div class="bg-white rounded-lg p-6 card-shadow metric-card">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-plug text-green-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">活跃接口</p>
                            <p class="text-2xl font-bold text-gray-900">85</p>
                            <!--<p class="text-sm text-gray-500">/ 120 总接口</p>-->
                        </div>
                    </div>
                </div>

                <!-- 成功率 -->
                <div class="bg-white rounded-lg p-6 card-shadow metric-card">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-check-circle text-purple-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">成功率</p>
                            <p class="text-2xl font-bold text-gray-900">99.8%</p>
                            <!--<p class="text-sm text-green-600">
                                <i class="fas fa-arrow-up mr-1"></i>
                                +0.2%
                            </p>-->
                        </div>
                    </div>
                </div>

                <!-- 平均响应时间 -->
                <div class="bg-white rounded-lg p-6 card-shadow metric-card">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-clock text-orange-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">平均响应时间</p>
                            <p class="text-2xl font-bold text-gray-900">245ms</p>
                            <!--<p class="text-sm text-green-600">
                                <i class="fas fa-arrow-down mr-1"></i>
                                -15ms
                            </p>-->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- 调用量趋势图 -->
                <div class="bg-white rounded-lg p-6 card-shadow">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">调用量趋势</h3>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs font-medium text-indigo-600 bg-indigo-100 rounded-full">7天</button>
                            <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:text-gray-700">30天</button>
                        </div>
                    </div>
                    <canvas id="callTrendChart" width="400" height="200"></canvas>
                </div>

                <!-- API使用分布 -->
                <div class="bg-white rounded-lg p-6 card-shadow">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">API使用分布</h3>
                        <button class="text-sm text-indigo-600 hover:text-indigo-500">查看详情</button>
                    </div>
                    <canvas id="apiDistributionChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- 系统使用情况 -->
            <!--<div class="bg-white rounded-lg p-6 card-shadow mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">系统使用情况</h3>
                <div class="space-y-4">
                    <div>
                        <div class="flex justify-between text-sm mb-2">
                            <span class="text-gray-600">用户管理系统API</span>
                            <span class="text-gray-900">1,250 次调用</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: 85%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between text-sm mb-2">
                            <span class="text-gray-600">订单管理系统API</span>
                            <span class="text-gray-900">890 次调用</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-600 h-2 rounded-full" style="width: 64%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between text-sm mb-2">
                            <span class="text-gray-600">库存管理系统API</span>
                            <span class="text-gray-900">560 次调用</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-yellow-600 h-2 rounded-full" style="width: 40%"></div>
                        </div>
                    </div>
                </div>
            </div>-->

            <!-- 最近活动 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- 最近调用 -->
                <div class="bg-white rounded-lg p-6 card-shadow">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">最近调用</h3>
                        <a href="#" class="text-sm text-indigo-600 hover:text-indigo-500">查看全部</a>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between py-3 border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">用户信息查询</p>
                                    <p class="text-xs text-gray-500">2025-08-02 13:22:22</p>
                                </div>
                            </div>
                            <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">成功</span>
                        </div>
                        <div class="flex items-center justify-between py-3 border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">订单状态更新</p>
                                    <p class="text-xs text-gray-500">2025-08-02 13:17:22</p>
                                </div>
                            </div>
                            <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">成功</span>
                        </div>
                        <div class="flex items-center justify-between py-3">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-red-400 rounded-full mr-3"></div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">库存数据同步</p>
                                    <p class="text-xs text-gray-500">2025-08-02 13:12:22</p>
                                </div>
                            </div>
                            <span class="text-xs text-red-600 bg-red-100 px-2 py-1 rounded-full">失败</span>
                        </div>
                    </div>
                </div>

                <!-- 系统通知 -->
                <!--<div class="bg-white rounded-lg p-6 card-shadow">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">系统通知</h3>
                        <a href="#" class="text-sm text-indigo-600 hover:text-indigo-500">查看全部</a>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle text-blue-500"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">新功能上线</p>
                                <p class="text-xs text-gray-500 mt-1">图像识别API新增人脸检测功能</p>
                                <p class="text-xs text-gray-400 mt-1">1小时前</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">配额提醒</p>
                                <p class="text-xs text-gray-500 mt-1">您的文本分析API配额即将用完</p>
                                <p class="text-xs text-gray-400 mt-1">3小时前</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-green-500"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">维护完成</p>
                                <p class="text-xs text-gray-500 mt-1">系统维护已完成，服务恢复正常</p>
                                <p class="text-xs text-gray-400 mt-1">昨天</p>
                            </div>
                        </div>
                    </div>
                </div>-->
            </div>
        </div>
    </div>

    <script>
        // 调用量趋势图
        const callTrendCtx = document.getElementById('callTrendChart').getContext('2d');
        new Chart(callTrendCtx, {
            type: 'line',
            data: {
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                datasets: [{
                    label: '调用量',
                    data: [1200, 1900, 1500, 2100, 1800, 1400, 1247],
                    borderColor: 'rgb(99, 102, 241)',
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // API使用分布图
        const apiDistributionCtx = document.getElementById('apiDistributionChart').getContext('2d');
        new Chart(apiDistributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['文本分析', '图像识别', '语音识别', '机器翻译'],
                datasets: [{
                    data: [45, 25, 20, 10],
                    backgroundColor: [
                        'rgb(99, 102, 241)',
                        'rgb(34, 197, 94)',
                        'rgb(251, 191, 36)',
                        'rgb(239, 68, 68)'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</body>
</html>
