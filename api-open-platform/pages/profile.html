<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 企业API开放平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .tab-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <img class="h-8 w-8" src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=32&h=32&fit=crop&crop=center" alt="Logo">
                    <span class="ml-3 text-xl font-bold text-gray-800">企业API开放平台</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="用户头像">
                        <span class="text-sm font-medium text-gray-700">张开发者</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">个人中心</h1>
            <p class="mt-2 text-gray-600">管理您的账户信息、API密钥和使用配额</p>
        </div>

        <!-- 标签页导航 -->
        <div class="mb-8">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    <button onclick="switchTab('profile')" id="profile-tab" 
                            class="tab-active py-2 px-1 border-b-2 border-transparent font-medium text-sm whitespace-nowrap">
                        <i class="fas fa-user mr-2"></i>基本信息
                    </button>
                    <button onclick="switchTab('keys')" id="keys-tab" 
                            class="text-gray-500 hover:text-gray-700 py-2 px-1 border-b-2 border-transparent font-medium text-sm whitespace-nowrap">
                        <i class="fas fa-key mr-2"></i>API密钥
                    </button>
                    <button onclick="switchTab('usage')" id="usage-tab"
                            class="text-gray-500 hover:text-gray-700 py-2 px-1 border-b-2 border-transparent font-medium text-sm whitespace-nowrap">
                        <i class="fas fa-chart-pie mr-2"></i>使用统计
                    </button>
                    <!--<button onclick="switchTab('enterprise')" id="enterprise-tab"
                            class="text-gray-500 hover:text-gray-700 py-2 px-1 border-b-2 border-transparent font-medium text-sm whitespace-nowrap">
                        <i class="fas fa-building mr-2"></i>企业认证
                    </button>-->
                </nav>
            </div>
        </div>

        <!-- 基本信息标签页 -->
        <div id="profile-content" class="tab-content">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- 用户信息卡片 -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg p-6 card-shadow">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-900">基本信息</h3>
                        </div>
                        
                        <form class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                                    <input type="text" value="zhangdev" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">企业名称</label>
                                    <input type="text" value="****信息科技有限公司"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">手机号码</label>
                                    <input type="tel" value="138****8888"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">邮箱地址</label>
                                    <input type="email" value="<EMAIL>" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                </div>

                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- API密钥标签页 -->
        <div id="keys-content" class="tab-content hidden">
            <div class="bg-white rounded-lg p-6 card-shadow">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">API密钥管理</h3>
                </div>

                <!-- 密钥列表 -->
                <div class="space-y-4">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div>
                                <h4 class="text-md font-medium text-gray-900">默认密钥</h4>
                                <p class="text-sm text-gray-500">您的默认API密钥</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">启用</span>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">Access Key</label>
                                <div class="flex items-center">
                                    <code class="text-sm text-gray-800 bg-gray-50 px-2 py-1 rounded flex-1">ak_1234567890abcdef</code>
                                    <button onclick="copyToClipboard('ak_1234567890abcdef')" 
                                            class="ml-2 text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-500 mb-1">Secret Key</label>
                                <div class="flex items-center">
                                    <code class="text-sm text-gray-800 bg-gray-50 px-2 py-1 rounded flex-1">sk_**********************</code>
                                    <button onclick="toggleSecretKey(this)" 
                                            class="ml-2 text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3 flex items-center justify-between text-xs text-gray-500">
                            <span>创建时间: 2024-01-15 10:30:00</span>
                            <span>最后使用: 2小时前</span>
                        </div>
                    </div>
                </div>

                <!-- 使用说明 -->
                <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">API密钥使用说明</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>请妥善保管您的Secret Key，不要在客户端代码中暴露</li>
                                    <li>如发现密钥泄露，请立即联系管理员重新生成</li>
                                    <li>每个用户仅拥有一个默认密钥，如需更多密钥请联系管理员</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用统计标签页 -->
        <div id="usage-content" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- 接口使用概览 -->
                <!--<div class="bg-white rounded-lg p-6 card-shadow">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">接口使用概览</h3>

                    <div class="space-y-6">
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">用户管理系统API</span>
                                <span class="text-sm text-gray-500">1,250 次调用</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>本月使用频率较高</span>
                                <span>上次调用: 2小时前</span>
                            </div>
                        </div>

                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">订单管理系统API</span>
                                <span class="text-sm text-gray-500">890 次调用</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-600 h-2 rounded-full" style="width: 64%"></div>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>使用频率正常</span>
                                <span>上次调用: 30分钟前</span>
                            </div>
                        </div>

                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">库存管理系统API</span>
                                <span class="text-sm text-gray-500">560 次调用</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-yellow-600 h-2 rounded-full" style="width: 40%"></div>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>使用频率较低</span>
                                <span>上次调用: 1天前</span>
                            </div>
                        </div>
                    </div>
                </div>-->

                <!-- 使用统计 -->
                <div class="bg-white rounded-lg p-6 card-shadow">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">使用统计</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-chart-bar text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">今日调用</p>
                                    <p class="text-xs text-gray-500">API总调用次数</p>
                                </div>
                            </div>
                            <span class="text-lg font-bold text-gray-900">1,247</span>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-calendar-week text-green-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">本周调用</p>
                                    <p class="text-xs text-gray-500">过去7天总计</p>
                                </div>
                            </div>
                            <span class="text-lg font-bold text-gray-900">8,456</span>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-calendar-alt text-purple-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">本月调用</p>
                                    <p class="text-xs text-gray-500">当前月份总计</p>
                                </div>
                            </div>
                            <span class="text-lg font-bold text-gray-900">32,189</span>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-exclamation-triangle text-red-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">错误次数</p>
                                    <p class="text-xs text-gray-500">本月错误调用</p>
                                </div>
                            </div>
                            <span class="text-lg font-bold text-gray-900">23</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 安全设置标签页 -->
        <div id="security-content" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- 密码设置 -->
                <div class="bg-white rounded-lg p-6 card-shadow">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">密码设置</h3>
                    
                    <form class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">当前密码</label>
                            <input type="password" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                   placeholder="请输入当前密码">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">新密码</label>
                            <input type="password" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                   placeholder="请输入新密码">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">确认新密码</label>
                            <input type="password" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                   placeholder="请再次输入新密码">
                        </div>
                        <button type="submit" 
                                class="w-full gradient-bg text-white py-2 px-4 rounded-lg font-medium hover:opacity-90">
                            更新密码
                        </button>
                    </form>
                </div>

                <!-- 安全选项 -->
                <div class="bg-white rounded-lg p-6 card-shadow">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">安全选项</h3>
                    
                    <div class="space-y-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">两步验证</h4>
                                <p class="text-xs text-gray-500">使用手机验证码增强账户安全</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">登录通知</h4>
                                <p class="text-xs text-gray-500">新设备登录时发送邮件通知</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">API调用通知</h4>
                                <p class="text-xs text-gray-500">配额即将用完时发送提醒</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                    </div>

                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">登录历史</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between text-xs">
                                <span class="text-gray-600">北京市 - Chrome浏览器</span>
                                <span class="text-gray-500">2小时前</span>
                            </div>
                            <div class="flex items-center justify-between text-xs">
                                <span class="text-gray-600">上海市 - Safari浏览器</span>
                                <span class="text-gray-500">昨天</span>
                            </div>
                            <div class="flex items-center justify-between text-xs">
                                <span class="text-gray-600">广州市 - Firefox浏览器</span>
                                <span class="text-gray-500">3天前</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 重置所有标签样式
            document.querySelectorAll('[id$="-tab"]').forEach(tab => {
                tab.classList.remove('tab-active');
                tab.classList.add('text-gray-500', 'hover:text-gray-700');
            });
            
            // 显示选中的内容
            document.getElementById(tabName + '-content').classList.remove('hidden');
            
            // 激活选中的标签
            const activeTab = document.getElementById(tabName + '-tab');
            activeTab.classList.add('tab-active');
            activeTab.classList.remove('text-gray-500', 'hover:text-gray-700');
        }

        // 创建API密钥
        function createAPIKey() {
            alert('创建API密钥功能');
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('已复制到剪贴板');
            });
        }

        // 切换Secret Key显示
        function toggleSecretKey(button) {
            const codeElement = button.previousElementSibling;
            const icon = button.querySelector('i');
            
            if (codeElement.textContent.includes('****')) {
                codeElement.textContent = 'sk_abcdef1234567890abcdef1234567890';
                icon.className = 'fas fa-eye-slash';
            } else {
                codeElement.textContent = 'sk_**********************';
                icon.className = 'fas fa-eye';
            }
        }
    </script>
</body>
</html>
