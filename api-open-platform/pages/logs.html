<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调用日志 - 企业API开放平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .status-success { background: #10b981; }
        .status-error { background: #ef4444; }
        .status-warning { background: #f59e0b; }
        .log-row:hover {
            background-color: #f9fafb;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <img class="h-8 w-8" src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=32&h=32&fit=crop&crop=center" alt="Logo">
                    <span class="ml-3 text-xl font-bold text-gray-800">企业API开放平台</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-3">
                        <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="用户头像">
                        <span class="text-sm font-medium text-gray-700">张开发者</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">调用日志</h1>
            <p class="mt-2 text-gray-600">查看API调用记录、分析使用情况和排查问题</p>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-bar text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">今日调用</p>
                        <p class="text-2xl font-bold text-gray-900">1,247</p>
                        <!--<p class="text-sm text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i>+12.5%
                        </p>-->
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check-circle text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">成功率</p>
                        <p class="text-2xl font-bold text-gray-900">99.8%</p>
                        <!--<p class="text-sm text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i>+0.2%
                        </p>-->
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-clock text-orange-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">平均响应时间</p>
                        <p class="text-2xl font-bold text-gray-900">245ms</p>
                        <!--<p class="text-sm text-green-600">
                            <i class="fas fa-arrow-down mr-1"></i>-15ms
                        </p>-->
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">错误次数</p>
                        <p class="text-2xl font-bold text-gray-900">3</p>
                        <!--<p class="text-sm text-red-600">
                            <i class="fas fa-arrow-up mr-1"></i>+1
                        </p>-->
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 调用趋势图 -->
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">调用趋势</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-xs font-medium text-indigo-600 bg-indigo-100 rounded-full">24小时</button>
                        <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:text-gray-700">7天</button>
                        <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:text-gray-700">30天</button>
                    </div>
                </div>
                <canvas id="callTrendChart" width="400" height="200"></canvas>
            </div>

            <!-- 状态分布图 -->
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">状态分布</h3>
                </div>
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- 筛选和搜索 -->
        <div class="bg-white rounded-lg p-6 shadow-sm mb-8">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <!-- 时间范围 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option>今天</option>
                        <option>昨天</option>
                        <option>最近7天</option>
                        <option>最近30天</option>
                        <option>自定义</option>
                    </select>
                </div>

                <!-- API接口 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">API接口</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option>全部接口</option>
                        <option>文本情感分析</option>
                        <option>图像识别</option>
                        <option>语音识别</option>
                        <option>机器翻译</option>
                    </select>
                </div>

                <!-- 状态码 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">状态码</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option>全部状态</option>
                        <option>200 成功</option>
                        <option>400 请求错误</option>
                        <option>401 认证失败</option>
                        <option>429 限流</option>
                        <option>500 服务器错误</option>
                    </select>
                </div>

                <!-- 搜索 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">搜索</label>
                    <div class="relative">
                        <input type="text" placeholder="请求ID或关键词" 
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-end space-x-2">
                    <button class="gradient-bg text-white px-4 py-2 rounded-lg font-medium hover:opacity-90">
                        <i class="fas fa-search mr-2"></i>查询
                    </button>
                    <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50">
                        <i class="fas fa-redo mr-2"></i>重置
                    </button>
                </div>
            </div>
        </div>

        <!-- 日志列表 -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">调用记录</h3>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-500">共 1,247 条记录</span>
                    </div>
                </div>
            </div>

            <!-- 表格头部 -->
            <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <div class="grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div class="col-span-2">时间</div>
                    <div class="col-span-2">请求ID</div>
                    <div class="col-span-2">API接口</div>
                    <div class="col-span-1">方法</div>
                    <div class="col-span-1">状态码</div>
                    <div class="col-span-1">响应时间</div>
                    <div class="col-span-2">IP地址</div>
                    <div class="col-span-1">操作</div>
                </div>
            </div>

            <!-- 表格内容 -->
            <div class="divide-y divide-gray-200">
                <!-- 日志记录1 -->
                <div class="log-row px-6 py-4 cursor-pointer" onclick="showLogDetail('req_001')">
                    <div class="grid grid-cols-12 gap-4 items-center">
                        <div class="col-span-2 text-sm text-gray-900">
                            2024-02-20<br>
                            <span class="text-xs text-gray-500">14:30:25</span>
                        </div>
                        <div class="col-span-2">
                            <code class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">req_1708412425001</code>
                        </div>
                        <div class="col-span-2 text-sm text-gray-900">
                            文本情感分析
                            <br><span class="text-xs text-gray-500">/v1/nlp/sentiment</span>
                        </div>
                        <div class="col-span-1">
                            <span class="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded">POST</span>
                        </div>
                        <div class="col-span-1">
                            <span class="status-success text-white text-xs font-medium px-2 py-1 rounded">200</span>
                        </div>
                        <div class="col-span-1 text-sm text-gray-900">245ms</div>
                        <div class="col-span-2 text-sm text-gray-500">
                            *************
                        </div>
                        <div class="col-span-1">
                            <button class="text-indigo-600 hover:text-indigo-500 text-sm">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 日志记录2 -->
                <div class="log-row px-6 py-4 cursor-pointer" onclick="showLogDetail('req_002')">
                    <div class="grid grid-cols-12 gap-4 items-center">
                        <div class="col-span-2 text-sm text-gray-900">
                            2024-02-20<br>
                            <span class="text-xs text-gray-500">14:28:15</span>
                        </div>
                        <div class="col-span-2">
                            <code class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">req_1708412295002</code>
                        </div>
                        <div class="col-span-2 text-sm text-gray-900">
                            图像识别
                            <br><span class="text-xs text-gray-500">/v1/vision/recognition</span>
                        </div>
                        <div class="col-span-1">
                            <span class="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded">POST</span>
                        </div>
                        <div class="col-span-1">
                            <span class="status-success text-white text-xs font-medium px-2 py-1 rounded">200</span>
                        </div>
                        <div class="col-span-1 text-sm text-gray-900">1.2s</div>
                        <div class="col-span-2 text-sm text-gray-500">
                            *************
                        </div>
                        <div class="col-span-1">
                            <button class="text-indigo-600 hover:text-indigo-500 text-sm">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 日志记录3 - 错误示例 -->
                <div class="log-row px-6 py-4 cursor-pointer" onclick="showLogDetail('req_003')">
                    <div class="grid grid-cols-12 gap-4 items-center">
                        <div class="col-span-2 text-sm text-gray-900">
                            2024-02-20<br>
                            <span class="text-xs text-gray-500">14:25:10</span>
                        </div>
                        <div class="col-span-2">
                            <code class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">req_1708412110003</code>
                        </div>
                        <div class="col-span-2 text-sm text-gray-900">
                            语音识别
                            <br><span class="text-xs text-gray-500">/v1/speech/recognition</span>
                        </div>
                        <div class="col-span-1">
                            <span class="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded">POST</span>
                        </div>
                        <div class="col-span-1">
                            <span class="status-error text-white text-xs font-medium px-2 py-1 rounded">400</span>
                        </div>
                        <div class="col-span-1 text-sm text-gray-900">50ms</div>
                        <div class="col-span-2 text-sm text-gray-500">
                            *************
                        </div>
                        <div class="col-span-1">
                            <button class="text-indigo-600 hover:text-indigo-500 text-sm">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 日志记录4 -->
                <div class="log-row px-6 py-4 cursor-pointer" onclick="showLogDetail('req_004')">
                    <div class="grid grid-cols-12 gap-4 items-center">
                        <div class="col-span-2 text-sm text-gray-900">
                            2024-02-20<br>
                            <span class="text-xs text-gray-500">14:20:45</span>
                        </div>
                        <div class="col-span-2">
                            <code class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">req_1708411845004</code>
                        </div>
                        <div class="col-span-2 text-sm text-gray-900">
                            机器翻译
                            <br><span class="text-xs text-gray-500">/v1/nlp/translation</span>
                        </div>
                        <div class="col-span-1">
                            <span class="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded">POST</span>
                        </div>
                        <div class="col-span-1">
                            <span class="status-success text-white text-xs font-medium px-2 py-1 rounded">200</span>
                        </div>
                        <div class="col-span-1 text-sm text-gray-900">180ms</div>
                        <div class="col-span-2 text-sm text-gray-500">
                            *************
                        </div>
                        <div class="col-span-1">
                            <button class="text-indigo-600 hover:text-indigo-500 text-sm">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 日志记录5 - 限流示例 -->
                <div class="log-row px-6 py-4 cursor-pointer" onclick="showLogDetail('req_005')">
                    <div class="grid grid-cols-12 gap-4 items-center">
                        <div class="col-span-2 text-sm text-gray-900">
                            2024-02-20<br>
                            <span class="text-xs text-gray-500">14:18:30</span>
                        </div>
                        <div class="col-span-2">
                            <code class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">req_1708411710005</code>
                        </div>
                        <div class="col-span-2 text-sm text-gray-900">
                            文本分类
                            <br><span class="text-xs text-gray-500">/v1/nlp/classification</span>
                        </div>
                        <div class="col-span-1">
                            <span class="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded">POST</span>
                        </div>
                        <div class="col-span-1">
                            <span class="status-warning text-white text-xs font-medium px-2 py-1 rounded">429</span>
                        </div>
                        <div class="col-span-1 text-sm text-gray-900">10ms</div>
                        <div class="col-span-2 text-sm text-gray-500">
                            *************
                        </div>
                        <div class="col-span-1">
                            <button class="text-indigo-600 hover:text-indigo-500 text-sm">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="bg-white px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">20</span> 条，共 <span class="font-medium">1,247</span> 条记录
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            上一页
                        </button>
                        <button class="px-3 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700">
                            1
                        </button>
                        <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            2
                        </button>
                        <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            3
                        </button>
                        <span class="px-3 py-2 text-sm font-medium text-gray-500">...</span>
                        <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            63
                        </button>
                        <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 日志详情模态框 -->
    <div id="logDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">调用详情</h3>
                        <button onclick="closeLogDetail()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div id="logDetailContent">
                        <!-- 动态加载详情内容 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 调用趋势图
        const callTrendCtx = document.getElementById('callTrendChart').getContext('2d');
        new Chart(callTrendCtx, {
            type: 'line',
            data: {
                labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                datasets: [{
                    label: '调用次数',
                    data: [45, 23, 78, 156, 234, 189, 98],
                    borderColor: 'rgb(99, 102, 241)',
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // 状态分布图
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['200 成功', '400 请求错误', '401 认证失败', '429 限流', '500 服务器错误'],
                datasets: [{
                    data: [1244, 2, 0, 1, 0],
                    backgroundColor: [
                        '#10b981',
                        '#ef4444',
                        '#f59e0b',
                        '#8b5cf6',
                        '#6b7280'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // 显示日志详情
        function showLogDetail(requestId) {
            const modal = document.getElementById('logDetailModal');
            const content = document.getElementById('logDetailContent');
            
            // 模拟加载详情数据
            content.innerHTML = `
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-md font-semibold text-gray-900 mb-3">基本信息</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">请求ID:</span>
                                    <code class="text-gray-800">${requestId}</code>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">API接口:</span>
                                    <span class="text-gray-800">文本情感分析</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">请求方法:</span>
                                    <span class="text-blue-600 font-medium">POST</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">状态码:</span>
                                    <span class="text-green-600 font-medium">200</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">响应时间:</span>
                                    <span class="text-gray-800">245ms</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">请求时间:</span>
                                    <span class="text-gray-800">2024-02-20 14:30:25</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">IP地址:</span>
                                    <span class="text-gray-800">*************</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">API密钥:</span>
                                    <code class="text-gray-800">ak_1234567890abcdef</code>
                                </div>
                            </div>
                        </div>
                        <!--<div>
                            <h4 class="text-md font-semibold text-gray-900 mb-3">客户端信息</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">IP地址:</span>
                                    <span class="text-gray-800">*************</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">User-Agent:</span>
                                    <span class="text-gray-800 truncate">Mozilla/5.0 (Windows NT 10.0; Win64; x64)</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">API密钥:</span>
                                    <code class="text-gray-800">ak_1234567890abcdef</code>
                                </div>
                            </div>
                        </div>-->
                    </div>
                    
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-3">请求头</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <pre class="text-sm text-gray-800">Content-Type: application/json
Authorization: Bearer ak_1234567890abcdef
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)
Accept: application/json</pre>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-3">请求体</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <pre class="text-sm text-gray-800">{
  "text": "这个产品真的很棒，我非常喜欢！",
  "language": "zh-CN"
}</pre>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-3">响应体</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <pre class="text-sm text-gray-800">{
  "code": 200,
  "message": "success",
  "data": {
    "sentiment": "positive",
    "confidence": 0.95,
    "scores": {
      "positive": 0.95,
      "negative": 0.03,
      "neutral": 0.02
    }
  }
}</pre>
                        </div>
                    </div>
                </div>
            `;
            
            modal.classList.remove('hidden');
        }

        // 关闭日志详情
        function closeLogDetail() {
            document.getElementById('logDetailModal').classList.add('hidden');
        }

        // 点击模态框外部关闭
        document.getElementById('logDetailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeLogDetail();
            }
        });
    </script>
</body>
</html>
