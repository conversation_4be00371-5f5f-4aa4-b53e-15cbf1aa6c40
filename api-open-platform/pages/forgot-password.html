<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>忘记密码 - 企业API开放平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .login-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .form-container {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
    </style>
</head>
<body class="login-bg min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <!-- Logo和标题 -->
        <div class="text-center mb-8">
            <div class="flex justify-center mb-4">
                <img class="h-12 w-12" src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=48&h=48&fit=crop&crop=center" alt="Logo">
            </div>
            <h2 class="text-3xl font-bold text-white mb-2">忘记密码</h2>
            <p class="text-white/80">通过手机验证码重置您的密码</p>
        </div>

        <!-- 忘记密码表单 -->
        <div class="form-container rounded-2xl shadow-2xl p-8">
            <form class="space-y-6">
                
                <div class="mb-4">
                    <label for="forgot-phone" class="block text-sm font-medium text-gray-700 mb-2">
                        手机号码
                    </label>
                    <input id="forgot-phone" name="forgot-phone" type="tel" 
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="请输入注册手机号">
                </div>
                
                <div class="flex space-x-3 mb-4">
                    <div class="flex-1">
                        <label for="verification-code" class="block text-sm font-medium text-gray-700 mb-2">
                            验证码
                        </label>
                        <input id="verification-code" name="verification-code" type="text" 
                               class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                               placeholder="请输入验证码">
                    </div>
                    <button type="button" 
                            class="px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 mt-6">
                        获取验证码
                    </button>
                </div>
                
                <div class="mb-4">
                    <label for="new-password" class="block text-sm font-medium text-gray-700 mb-2">
                        新密码
                    </label>
                    <input id="new-password" name="new-password" type="password" 
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="请输入新密码">
                </div>
                
                <div class="mb-4">
                    <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-2">
                        确认新密码
                    </label>
                    <input id="confirm-password" name="confirm-password" type="password" 
                           class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="请再次输入新密码">
                </div>
                
                <div class="flex space-x-3">
                    <button type="button" 
                            class="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            onclick="window.location.href='login.html'">
                        返回登录
                    </button>
                    <button type="submit" 
                            class="flex-1 py-2 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        重置密码
                    </button>
                </div>
            </form>
        </div>

        <!-- 安全提示 -->
        <div class="mt-8 text-center">
            <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div class="flex items-center justify-center text-white/80 text-sm">
                    <i class="fas fa-shield-alt mr-2"></i>
                    <span>您的数据受到256位SSL加密保护</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            
            if (newPassword !== confirmPassword) {
                alert('两次输入的密码不一致');
                return;
            }
            
            if (newPassword.length < 8) {
                alert('密码长度至少8位');
                return;
            }
            
            // 模拟重置密码过程
            const button = document.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>重置中...';
            button.disabled = true;
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                alert('密码重置成功！即将跳转到登录页面。');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1000);
            }, 2000);
        });
    </script>
</body>
</html>