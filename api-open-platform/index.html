<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业API开放平台 - 原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .prototype-container {
            width: 1920px;
            margin: 0 auto;
            background: #f8fafc;
        }
        .page-frame {
            width: 100%;
            height: 1080px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .page-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            font-weight: bold;
            border-radius: 6px 6px 0 0;
        }
        .nav-menu {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 20px;
            z-index: 1000;
            width: 200px;
        }
        .nav-menu a {
            display: block;
            padding: 8px 12px;
            color: #374151;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 4px;
            transition: all 0.2s;
        }
        .nav-menu a:hover {
            background: #f3f4f6;
            color: #1f2937;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 导航菜单 -->
    <div class="nav-menu">
        <h3 class="text-lg font-bold mb-4 text-gray-800">页面导航</h3>
        <a href="#home" onclick="scrollToPage('home')"><i class="fas fa-home mr-2"></i>首页</a>
        <a href="#login" onclick="scrollToPage('login')"><i class="fas fa-sign-in-alt mr-2"></i>登录页</a>
<!--        <a href="#register" onclick="scrollToPage('register')"><i class="fas fa-user-plus mr-2"></i>注册页</a>-->
<!--        <a href="#forgot-password" onclick="scrollToPage('forgot-password')"><i class="fas fa-key mr-2"></i>忘记密码</a>-->
        <a href="#dashboard" onclick="scrollToPage('dashboard')"><i class="fas fa-tachometer-alt mr-2"></i>控制台</a>
        <a href="#api-docs" onclick="scrollToPage('api-docs')"><i class="fas fa-book mr-2"></i>API文档</a>

        <a href="#profile" onclick="scrollToPage('profile')"><i class="fas fa-user mr-2"></i>个人中心</a>
        <a href="#logs" onclick="scrollToPage('logs')"><i class="fas fa-list-alt mr-2"></i>调用日志</a>
        <a href="#monitoring" onclick="scrollToPage('monitoring')"><i class="fas fa-chart-line mr-2"></i>监控告警</a>
        <a href="#settings" onclick="scrollToPage('settings')"><i class="fas fa-cog mr-2"></i>系统管理</a>
    </div>

    <div class="prototype-container">
        <div class="text-center py-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-4">企业API开放平台 - 高保真原型</h1>
            <p class="text-gray-600">以下是所有核心界面的完整原型设计</p>
        </div>

        <!-- 首页 -->
        <div id="home" class="page-frame">
            <div class="page-title"> 首页 - 平台介绍与API概览</div>
            <iframe src="pages/home.html" width="100%" height="1050" frameborder="0"></iframe>
        </div>

        <!-- 登录页 -->
        <div id="login" class="page-frame">
            <div class="page-title"> 登录页 - 用户登录</div>
            <iframe src="pages/login.html" width="100%" height="1050" frameborder="0"></iframe>
        </div>

        <!-- 注册页 -->
        <!--<div id="register" class="page-frame">
            <div class="page-title"> 注册页 - 用户注册</div>
            <iframe src="pages/register.html" width="100%" height="1050" frameborder="0"></iframe>
        </div>-->

        <!-- 忘记密码页 -->
        <!--<div id="forgot-password" class="page-frame">
            <div class="page-title"> 忘记密码 - 手机验证码重置密码</div>
            <iframe src="pages/forgot-password.html" width="100%" height="1050" frameborder="0"></iframe>
        </div>-->

        <!-- 控制台 -->
        <div id="dashboard" class="page-frame">
            <div class="page-title"> 控制台 - 数据概览与配额监控</div>
            <iframe src="pages/dashboard.html" width="100%" height="1050" frameborder="0"></iframe>
        </div>

        <!-- API文档 -->
        <div id="api-docs" class="page-frame">
            <div class="page-title"> API文档 - 接口文档与分类</div>
            <iframe src="pages/api-docs.html" width="100%" height="1050" frameborder="0"></iframe>
        </div>

        <!-- 个人中心 -->
        <div id="profile" class="page-frame">
            <div class="page-title"> 个人中心 - 用户信息与密钥管理</div>
            <iframe src="pages/profile.html" width="100%" height="1050" frameborder="0"></iframe>
        </div>

        <!-- 调用日志 -->
        <div id="logs" class="page-frame">
            <div class="page-title"> 调用日志 - API请求记录与分析</div>
            <iframe src="pages/logs.html" width="100%" height="1050" frameborder="0"></iframe>
        </div>

        <!-- 监控告警 -->
        <div id="monitoring" class="page-frame">
            <div class="page-title"> 监控告警 - 系统监控与告警配置</div>
            <iframe src="pages/monitoring.html" width="100%" height="1050" frameborder="0"></iframe>
        </div>

        <!-- 系统管理 -->
        <div id="settings" class="page-frame">
            <div class="page-title"> 系统管理 - 限流配置与系统管理</div>
            <iframe src="pages/settings.html" width="100%" height="1050" frameborder="0"></iframe>
        </div>
    </div>

    <script>
        function scrollToPage(pageId) {
            document.getElementById(pageId).scrollIntoView({
                behavior: 'smooth'
            });
        }
    </script>
</body>
</html>