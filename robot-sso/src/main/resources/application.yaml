server:
  port: 10001
  tomcat:
    uri-encoding: utf-8
  servlet:
    context-path: /robot-sso

spring:
  application:
    name: robot-sso
  sleuth:
    sampler:
      probability: 1.0  # 采样率，1.0表示100%采样
    zipkin:
      enabled: false  # 如果不需要zipkin，可以禁用

#调用sso-sdk需要引入的配置
#  url：
#  【dev】：https://dev-sso-api.robot-sew.com
#  【sit】：https://sit-sso-api.robot-sew.com
#  【uat】：https://uat-sso-api.robot-sew.com
#  【prod】：https://sso-api.robot-sew.com
saas:
  sso:
    sdk:
      admin:
        ## 是否启用admin feign接口 默认为true
        enabled: true
        ## 在工程中引用saas-admin-sdk-for-microservice 依赖的情况下，会注入该依赖中的 SsoTokenFilter ，将请求头 ssoToken 的值解析成 SsoUserDTO 对象，并且放入 SaasSsoContext 上下文中，该过滤器必须搭配 saas.sso.sdk.sso.public-key 的值使用，缺省为true
        enable: true
        ### 封装 saas-admin 、 saas-sso 接口请求地址
#        url: https://sit-sso-api.robot-sew.com
        url: http://localhost:8801
      ### 系统配置列表，为封装 saas-admin 接口提供安全性保护，必须配置，否则无法调用
      systems:
        - id: 83
          code: SSO
          secret-key: 3f6c103473864e13b28d6bb3a940a90
    public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAn8PGETpI+f0tL9zx9uAyQE5BOBSPB8rvz+mvLftNeL53CvGA/xl4eYg3mNBl3NsliQTVzV1iO5CF4GS9vBW3jzErrT74PmRRGQ8vuSJd6o/hZLiHUGn6GBLOH8wzh+Hz382DCx5GwOpCAuKQHsn5+uuK/TKMP6UjIYAIRvH1vpSEyBEhXdqB9CoWZaG0EYPJd9HFWg4z2x2+Bjog/39qAICIaSa1thjL/xSELgKGd2W1/SG09KU07olv9A1ppZpRCMrzNp9DyCaLJ87sjbozEg6SOHzV6+Lzb9AEU+geUK36hvqcF+J1gvJBWekepQ4uNUHj1ansdNOqw2l+hxqS5wIDAQAB