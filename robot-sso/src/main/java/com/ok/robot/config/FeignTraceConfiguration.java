package com.ok.robot.config;

import com.ok.robot.context.TraceContext;
import com.ok.robot.util.TraceUtils;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

/**
 * Feign客户端TraceId传递配置
 */
@Configuration
public class FeignTraceConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(FeignTraceConfiguration.class);

    @Autowired
    private TraceUtils traceUtils;

    /**
     * Feign请求拦截器，自动添加TraceId到请求头
     */
    @Bean
    public RequestInterceptor traceIdRequestInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                // 优先从TraceContext获取
                String traceId = TraceContext.getTraceId();
                String spanId = TraceContext.getSpanId();
                String ssoToken = TraceContext.getSsoToken();

                // 如果TraceContext没有，尝试从TraceUtils获取
                if (StringUtils.isEmpty(traceId)) {
                    traceId = traceUtils.getCurrentTraceId();
                    spanId = traceUtils.getCurrentSpanId();
                }

                if (!StringUtils.isEmpty(traceId)) {
                    template.header("X-Trace-Id", traceId);
                    logger.debug("Added traceId {} to Feign request: {} {}",
                               traceId, template.method(), template.url());
                }

                if (!StringUtils.isEmpty(spanId)) {
                    template.header("X-Span-Id", spanId);
                    logger.debug("Added spanId {} to Feign request: {} {}",
                               spanId, template.method(), template.url());
                }

                // 传递ssoToken
                if (!StringUtils.isEmpty(ssoToken)) {
                    template.header("ssoToken", ssoToken);
                    logger.debug("Added ssoToken to Feign request: {} {}",
                               template.method(), template.url());
                }
            }
        };
    }
}
