package com.ok.robot.controller;

import com.ok.robot.util.TraceUtils;
import com.zjkj.saas.admin.sdk.client.UserApiClient;
import com.zjkj.saas.admin.sdk.query.UserLeaderApiQuery;
import com.zjkj.saas.admin.sdk.response.ResponseDTO;
import com.zjkj.saas.sso.sdk.client.AuthenApiClient;
import com.zjkj.saas.sso.sdk.command.ApiLoginCommand;
import com.zjkj.saas.sso.sdk.dto.SsoAuthFatDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;


@RestController
@RequestMapping("/home")
public class HomeController {

    private static final Logger logger = LoggerFactory.getLogger(HomeController.class);

//    @Resource
//    UserApiClient userApiClient;

    @Resource
    AuthenApiClient authenApiClient;

    @Autowired
    private TraceUtils traceUtils;

    @PostMapping("/explore")
    public ResponseDTO explore(@RequestBody(required = false) ApiLoginCommand apiLoginCommand) {
        String traceId = traceUtils.getCurrentTraceId();
        logger.info("Processing explore request with traceId: {}", traceId);

        apiLoginCommand.setLoginFromSystemCode("WMS");
        apiLoginCommand.setLoginType(4);
        apiLoginCommand.setUsername("<EMAIL>");
        apiLoginCommand.setPassword("1122!!@@");

        ResponseDTO<SsoAuthFatDTO> login = authenApiClient.login(apiLoginCommand);

        return login;
    }

}
