package com.ok.robot.context;

import org.springframework.util.StringUtils;

/**
 * TraceId上下文管理器
 * 使用ThreadLocal存储当前线程的TraceId信息
 */
public class TraceContext {

    private static final ThreadLocal<String> TRACE_ID_HOLDER = new ThreadLocal<>();
    private static final ThreadLocal<String> SPAN_ID_HOLDER = new ThreadLocal<>();
    private static final ThreadLocal<String> SSO_TOKEN_HOLDER = new ThreadLocal<>();

    /**
     * 设置TraceId
     */
    public static void setTraceId(String traceId) {
        if (!StringUtils.isEmpty(traceId)) {
            TRACE_ID_HOLDER.set(traceId);
        }
    }

    /**
     * 获取TraceId
     */
    public static String getTraceId() {
        return TRACE_ID_HOLDER.get();
    }

    /**
     * 设置SpanId
     */
    public static void setSpanId(String spanId) {
        if (!StringUtils.isEmpty(spanId)) {
            SPAN_ID_HOLDER.set(spanId);
        }
    }

    /**
     * 获取SpanId
     */
    public static String getSpanId() {
        return SPAN_ID_HOLDER.get();
    }

    /**
     * 设置SSO Token
     */
    public static void setSsoToken(String ssoToken) {
        if (!StringUtils.isEmpty(ssoToken)) {
            SSO_TOKEN_HOLDER.set(ssoToken);
        }
    }

    /**
     * 获取SSO Token
     */
    public static String getSsoToken() {
        return SSO_TOKEN_HOLDER.get();
    }

    /**
     * 清除当前线程的所有上下文信息
     */
    public static void clear() {
        TRACE_ID_HOLDER.remove();
        SPAN_ID_HOLDER.remove();
        SSO_TOKEN_HOLDER.remove();
    }

    /**
     * 清除TraceId
     */
    public static void clearTraceId() {
        TRACE_ID_HOLDER.remove();
    }

    /**
     * 清除SpanId
     */
    public static void clearSpanId() {
        SPAN_ID_HOLDER.remove();
    }

    /**
     * 清除SSO Token
     */
    public static void clearSsoToken() {
        SSO_TOKEN_HOLDER.remove();
    }

    /**
     * 获取完整的追踪信息
     */
    public static TraceInfo getTraceInfo() {
        return new TraceInfo(getTraceId(), getSpanId(), getSsoToken());
    }

    /**
     * 追踪信息实体类
     */
    public static class TraceInfo {
        private final String traceId;
        private final String spanId;
        private final String ssoToken;

        public TraceInfo(String traceId, String spanId, String ssoToken) {
            this.traceId = traceId;
            this.spanId = spanId;
            this.ssoToken = ssoToken;
        }

        public String getTraceId() {
            return traceId;
        }

        public String getSpanId() {
            return spanId;
        }

        public String getSsoToken() {
            return ssoToken;
        }

        @Override
        public String toString() {
            return "TraceInfo{" +
                    "traceId='" + traceId + '\'' +
                    ", spanId='" + spanId + '\'' +
                    ", ssoToken='" + (ssoToken != null ? "***" : null) + '\'' +
                    '}';
        }
    }
}
