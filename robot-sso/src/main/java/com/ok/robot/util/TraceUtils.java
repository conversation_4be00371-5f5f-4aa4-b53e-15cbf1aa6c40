package com.ok.robot.util;

import brave.Tracer;
import brave.propagation.TraceContext;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * TraceId工具类
 * 支持多种方式获取TraceId
 */
@Component
public class TraceUtils {

    @Autowired(required = false)
    private Tracer tracer;

    /**
     * 获取当前请求的TraceId
     * 优先级：Sleuth > MDC > 生成新的
     */
    public String getCurrentTraceId() {
        // 1. 尝试从Sleuth获取
        String traceId = getSleuthTraceId();
        if (traceId != null) {
            return traceId;
        }

        // 2. 尝试从MDC获取
        traceId = getMDCTraceId();
        if (traceId != null) {
            return traceId;
        }

        // 3. 生成新的TraceId
        return generateTraceId();
    }

    /**
     * 从Spring Cloud Sleuth获取TraceId
     */
    public String getSleuthTraceId() {
        if (tracer != null) {
            TraceContext traceContext = tracer.currentSpan() != null ? 
                tracer.currentSpan().context() : null;
            if (traceContext != null) {
                return traceContext.traceId() + "";
            }
        }
        return null;
    }

    /**
     * 从MDC获取TraceId
     */
    public String getMDCTraceId() {
        return MDC.get("traceId");
    }

    /**
     * 设置TraceId到MDC
     */
    public void setTraceIdToMDC(String traceId) {
        MDC.put("traceId", traceId);
    }

    /**
     * 清除MDC中的TraceId
     */
    public void clearTraceIdFromMDC() {
        MDC.remove("traceId");
    }

    /**
     * 生成新的TraceId
     */
    public String generateTraceId() {
        return System.currentTimeMillis() + "-" + 
               Thread.currentThread().getId() + "-" + 
               (int)(Math.random() * 10000);
    }

    /**
     * 获取当前SpanId
     */
    public String getCurrentSpanId() {
        if (tracer != null && tracer.currentSpan() != null) {
            return tracer.currentSpan().context().spanId() + "";
        }
        return null;
    }

    /**
     * 获取完整的追踪信息
     */
    public TraceInfo getCurrentTraceInfo() {
        return TraceInfo.builder()
            .traceId(getCurrentTraceId())
            .spanId(getCurrentSpanId())
            .build();
    }

    /**
     * 追踪信息实体类
     */
    public static class TraceInfo {
        private String traceId;
        private String spanId;

        public static TraceInfoBuilder builder() {
            return new TraceInfoBuilder();
        }

        public static class TraceInfoBuilder {
            private String traceId;
            private String spanId;

            public TraceInfoBuilder traceId(String traceId) {
                this.traceId = traceId;
                return this;
            }

            public TraceInfoBuilder spanId(String spanId) {
                this.spanId = spanId;
                return this;
            }

            public TraceInfo build() {
                TraceInfo info = new TraceInfo();
                info.traceId = this.traceId;
                info.spanId = this.spanId;
                return info;
            }
        }

        // Getters and Setters
        public String getTraceId() {
            return traceId;
        }

        public void setTraceId(String traceId) {
            this.traceId = traceId;
        }

        public String getSpanId() {
            return spanId;
        }

        public void setSpanId(String spanId) {
            this.spanId = spanId;
        }

        @Override
        public String toString() {
            return "TraceInfo{" +
                    "traceId='" + traceId + '\'' +
                    ", spanId='" + spanId + '\'' +
                    '}';
        }
    }
}
