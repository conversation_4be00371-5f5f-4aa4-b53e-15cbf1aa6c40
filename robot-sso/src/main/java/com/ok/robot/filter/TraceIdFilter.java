package com.ok.robot.filter;

import com.ok.robot.context.TraceContext;
import com.ok.robot.util.TraceUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * TraceId过滤器
 * 为每个请求生成或提取TraceId，并设置到MDC中
 */
@Component
@Order(1)
public class TraceIdFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(TraceIdFilter.class);

    private static final String TRACE_ID_HEADER = "X-Trace-Id";
    private static final String SPAN_ID_HEADER = "X-Span-Id";
    private static final String SSO_TOKEN_HEADER = "ssoToken";

    @Autowired
    private TraceUtils traceUtils;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        String traceId = null;
        String spanId = null;
        String ssoToken = null;

        try {
            // 1. 尝试从请求头获取TraceId和相关信息
            traceId = httpRequest.getHeader(TRACE_ID_HEADER);
            spanId = httpRequest.getHeader(SPAN_ID_HEADER);
            ssoToken = httpRequest.getHeader(SSO_TOKEN_HEADER);

            // 2. 如果请求头没有，尝试从Sleuth获取
            if (StringUtils.isEmpty(traceId)) {
                traceId = traceUtils.getSleuthTraceId();
                spanId = traceUtils.getCurrentSpanId();
            }

            // 3. 如果还是没有，生成新的TraceId
            if (StringUtils.isEmpty(traceId)) {
                traceId = traceUtils.generateTraceId();
                logger.debug("Generated new traceId: {}", traceId);
            }

            // 4. 设置到MDC中，供日志使用
            traceUtils.setTraceIdToMDC(traceId);
            if (!StringUtils.isEmpty(spanId)) {
                MDC.put("spanId", spanId);
            }

            // 5. 设置到ThreadLocal上下文中
            TraceContext.setTraceId(traceId);
            TraceContext.setSpanId(spanId);
            TraceContext.setSsoToken(ssoToken);

            // 6. 设置到响应头中
            httpResponse.setHeader(TRACE_ID_HEADER, traceId);
            if (!StringUtils.isEmpty(spanId)) {
                httpResponse.setHeader(SPAN_ID_HEADER, spanId);
            }

            logger.debug("Request [{}] {} with traceId: {}, spanId: {}, ssoToken: {}",
                        httpRequest.getMethod(),
                        httpRequest.getRequestURI(),
                        traceId,
                        spanId,
                        ssoToken != null ? "***" : null);

            // 7. 继续处理请求
            chain.doFilter(request, response);

        } finally {
            // 8. 清理MDC和ThreadLocal，避免内存泄漏
            traceUtils.clearTraceIdFromMDC();
            MDC.remove("spanId");
            TraceContext.clear();

            logger.debug("Cleaned up traceId: {} for request: {}",
                        traceId, httpRequest.getRequestURI());
        }
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        logger.info("TraceIdFilter initialized");
    }

    @Override
    public void destroy() {
        logger.info("TraceIdFilter destroyed");
    }
}
