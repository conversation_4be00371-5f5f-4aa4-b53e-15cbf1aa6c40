package com.ok.robot;

import com.ok.robot.context.TraceContext;
import com.ok.robot.util.TraceUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TraceIdTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private TraceUtils traceUtils;

    @Test
    public void testTraceIdGeneration() {
        String traceId = traceUtils.generateTraceId();
        assertNotNull(traceId);
        assertTrue(traceId.length() > 0);
        System.out.println("Generated TraceId: " + traceId);
    }

    @Test
    public void testTraceContext() {
        String testTraceId = "test-trace-123";
        String testSpanId = "test-span-456";
        String testSsoToken = "test-sso-token";

        // 设置上下文
        TraceContext.setTraceId(testTraceId);
        TraceContext.setSpanId(testSpanId);
        TraceContext.setSsoToken(testSsoToken);

        // 验证获取
        assertEquals(testTraceId, TraceContext.getTraceId());
        assertEquals(testSpanId, TraceContext.getSpanId());
        assertEquals(testSsoToken, TraceContext.getSsoToken());

        // 获取完整信息
        TraceContext.TraceInfo traceInfo = TraceContext.getTraceInfo();
        assertEquals(testTraceId, traceInfo.getTraceId());
        assertEquals(testSpanId, traceInfo.getSpanId());
        assertEquals(testSsoToken, traceInfo.getSsoToken());

        // 清理
        TraceContext.clear();
        assertNull(TraceContext.getTraceId());
        assertNull(TraceContext.getSpanId());
        assertNull(TraceContext.getSsoToken());
    }

    @Test
    public void testTraceIdInHttpRequest() {
        String customTraceId = "custom-trace-" + System.currentTimeMillis();
        
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-Trace-Id", customTraceId);
        headers.set("Content-Type", "application/json");

        HttpEntity<String> entity = new HttpEntity<>("{}", headers);

        ResponseEntity<String> response = restTemplate.exchange(
            "http://localhost:" + port + "/robot-sso/home/<USER>",
            HttpMethod.GET,
            entity,
            String.class
        );

        // 验证响应头包含TraceId
        String responseTraceId = response.getHeaders().getFirst("X-Trace-Id");
        assertEquals(customTraceId, responseTraceId);
        
        System.out.println("Request TraceId: " + customTraceId);
        System.out.println("Response TraceId: " + responseTraceId);
        System.out.println("Response Body: " + response.getBody());
    }

    @Test
    public void testAutoGeneratedTraceId() {
        ResponseEntity<String> response = restTemplate.getForEntity(
            "http://localhost:" + port + "/robot-sso/home/<USER>",
            String.class
        );

        // 验证响应头包含自动生成的TraceId
        String responseTraceId = response.getHeaders().getFirst("X-Trace-Id");
        assertNotNull(responseTraceId);
        assertTrue(responseTraceId.length() > 0);
        
        System.out.println("Auto-generated TraceId: " + responseTraceId);
        System.out.println("Response Body: " + response.getBody());
    }
}
